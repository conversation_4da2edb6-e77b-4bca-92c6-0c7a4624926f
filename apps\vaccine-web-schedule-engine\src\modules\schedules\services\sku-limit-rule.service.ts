import moment from 'moment';
import { ExtraDto } from '../dto/extra.dto';
import { PreProcessCurrentDto, PreProcessFutureDto } from '../dto/pre-process-data.dto';
import { Logger } from '@nestjs/common';
import { RuleRes } from '../dto/common';
import * as _ from 'lodash';
import { DEFAULT_DATE_SKU_LIMIT, FORMAT_DATE, RuleType } from 'apps/vaccine-web-schedule-engine/src/constants';

export class SkuLimitRuleService {
  logger = new Logger(SkuLimitRuleService.name);

  isPackage(current: PreProcessCurrentDto, arrFuture: PreProcessFutureDto[], extra?: ExtraDto): boolean {
    const arrSkuInCartExceptCurrent = extra?.listDiseaseGroupIdInCart.filter(
      (e) => e.diseaseGroupId !== current.diseaseGroupId,
    );
    this.logger.log(`[SKU LIMIT] SKU: ${current.diseaseGroupId} PACKAGE: ${arrSkuInCartExceptCurrent?.length}`);
    if (arrSkuInCartExceptCurrent?.length === 0 && extra?.timesBuy === 1) return false;
    // cùng 1 sp mua 2 trở lên là gói
    if (arrSkuInCartExceptCurrent?.length === 0 && extra?.timesBuy === 2) return true;
    // nhiều sản p thì group theo ngày > 2
    const arrDiseaseGroupIncart = arrSkuInCartExceptCurrent?.map((e) => e.diseaseGroupId);
    const arrDiseaseGroup = arrFuture
      .filter((e) => arrDiseaseGroupIncart?.includes(e.diseaseGroupId) && e.status === 0)
      ?.filter((e) => e.diseaseGroupId !== current.diseaseGroupId);

    // lấy những mũi mua trên giỏ thoy theo số lượng
    const arrRemain: PreProcessFutureDto[] = [];
    arrSkuInCartExceptCurrent.forEach((e) => {
      const arrDiseaseGroupFilter = arrDiseaseGroup.filter((a) => a.diseaseGroupId === e.diseaseGroupId);
      if (arrDiseaseGroupFilter?.length > 0) {
        arrRemain.push(...arrDiseaseGroupFilter?.slice(0, e.quantity));
      }
    });

    const arrFormatDate = _.uniq(
      arrRemain
        .map((e) => moment(e.date).utcOffset(7).format(FORMAT_DATE))
        .concat(moment(current.date).utcOffset(7).format(FORMAT_DATE)),
    );

    // group theo ngày nếu ngày > 2 gói
    // ngếu ngày === 1 thì k phải gói

    if (arrFormatDate?.length <= 1) return false;

    return true;
  }

  checkRuleSkuLimitForSuggest(
    date: Date,
    current: PreProcessCurrentDto,
    arrFuture: PreProcessFutureDto[],
    extra?: ExtraDto,
  ): Date {
    const arrDiseaseGroupId = extra?.arrDiseaseGroupIdDefined;
    const arrDiseaseGroup = extra?.osrLimitedQuota?.filter((e) => e.diseaseGroupId === current.diseaseGroupId);
    if (!(current?.status === 0 && arrDiseaseGroupId?.includes(current?.diseaseGroupId))) return date;
    if (this.isPackage(current, arrFuture, extra)) return date;
    // Today + DEFAULT_DATE_SKU_LIMIT ngày
    // check xem date còn quota k?
    const dateOffset = moment(date).utcOffset(7);
    const now = moment().utcOffset(7);

    const dateDiff = moment(dateOffset.format('YYYY-MM-DD')).diff(now.format('YYYY-MM-DD'), 'days');
    let dateNew = dateOffset;
    if (dateDiff < DEFAULT_DATE_SKU_LIMIT) {
      dateNew = now.add(DEFAULT_DATE_SKU_LIMIT, 'days');
    }

    let times = 1000;
    while (times) {
      times--;
      // Không có thông tin quote thì xem như là
      // Query theo ngày
      const quotaFind = arrDiseaseGroup?.find(
        (x) => moment(x.appointmentDate).utcOffset(7).format(FORMAT_DATE) === dateNew.format(FORMAT_DATE),
      );
      this.logger.debug(`[SKU LIMIT] DATE: ${dateNew.format()} QUOTA: ${quotaFind?.quota}`);
      if (!quotaFind) break;
      if (quotaFind?.quota > 0) break;
      dateNew.add(1, 'days');
      continue;
    }
    this.logger.debug(`[SKU LIMIT] DATE: ${dateNew.format()}`);
    return new Date(dateNew.format());
  }

  checkRuleSkuLimitForValidate(
    date: Date,
    current: PreProcessCurrentDto,
    arrFuture: PreProcessFutureDto[],
    rules: RuleRes[],
    extra?: ExtraDto,
  ) {
    const arrDiseaseGroupId = extra?.arrDiseaseGroupIdDefined;
    const arrDiseaseGroupQuota = extra?.osrLimitedQuota?.filter((e) => e.diseaseGroupId === current.diseaseGroupId);
    if (current?.screenType !== 'INDICATION') return false;
    if (!(current?.status === 0 && arrDiseaseGroupId?.includes(current?.diseaseGroupId))) return false;
    // chỉ định today thì pass
    const now = moment().utcOffset(7);
    const dateOffset = moment(date).utcOffset(7);
    const dateDiff = moment(dateOffset.format('YYYY-MM-DD')).diff(now.format('YYYY-MM-DD'), 'days');
    if (dateDiff === 0) return false;
    if (this.isPackage(current, arrFuture, extra)) return false;

    const dateNew = now.add(DEFAULT_DATE_SKU_LIMIT, 'days');

    if (dateDiff < DEFAULT_DATE_SKU_LIMIT) {
      rules.push({
        text: `Do số lượng vắc xin CÚM có hạn, khách mua ${
          current?.regimen?.diseaseGroup?.name
        } tiêm sau vui lòng đặt lịch từ ngày ${dateNew.format('DD/MM/YYYY')} trở đi`,
        type: 'DANGER',
        value: DEFAULT_DATE_SKU_LIMIT,
        ruleType: RuleType.HetTon,
        ruleName: 'Sản phẩm hết tồn',
      });
    } else {
      //check quota
      const quotaFind = arrDiseaseGroupQuota?.find(
        (x) => moment(x.appointmentDate).utcOffset(7).format(FORMAT_DATE) === dateOffset.format(FORMAT_DATE),
      );
      if (!quotaFind || quotaFind?.quota > 0) return false;
      rules.push({
        text: `Số lượng mũi tiêm ${current?.regimen?.diseaseGroup?.name} ngày ${dateOffset.format(
          'DD/MM/YYYY',
        )} đã đạt mức tối đa. Vui lòng chọn lại ngày hẹn khác`,
        type: 'DANGER',
        value: DEFAULT_DATE_SKU_LIMIT,
        ruleType: RuleType.HetTon,
        ruleName: 'Sản phẩm hết tồn',
      });
    }
    return true;
  }
}
