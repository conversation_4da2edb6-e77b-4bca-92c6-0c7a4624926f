import { IS3ModuleOptions, IS3OptionsFactory } from 'ict-nest-s3';
export class S3ConfigService implements IS3OptionsFactory {
  createElasticsearchOptions(): IS3ModuleOptions {
    return {
      configs: [
        {
          instanceName: process.env.S3_CONNECTION,
          accessKeyId: process.env.S3_KEY,
          secretAccessKey: process.env.S3_SECRET,
          bucketName: process.env.S3_BUCKET,
          region: process.env.S3_REGION,
          endpoint: process.env.S3_ENDPOINT,
        },
        {
          instanceName: process.env.S3_RSA_CONNECTION,
          accessKeyId: process.env.S3_RSA_KEY,
          secretAccessKey: process.env.S3_RSA_SECRET,
          bucketName: process.env.S3_RSA_BUCKET,
          region: process.env.S3_REGION,
          endpoint: process.env.S3_ENDPOINT,
        },
      ],
    };
  }
}
