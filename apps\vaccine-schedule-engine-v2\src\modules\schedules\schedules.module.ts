import { Module } from '@nestjs/common';
import { FamilyModule } from 'vac-nest-family';
import { VacHistoryModule } from 'vac-nest-history';
import { PIMAppModule } from 'vac-nest-pim-app';
import { RegimenModule } from 'vac-nest-regimen';
import { SchedulesController } from './controllers/schedules.controller';
import { ScheduleRulesService } from './services/schedule-rules.service';
import { SchedulesService } from './services/schedules.service';
import { IMSModule } from 'vac-nest-ims';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { AgeRuleService } from './services/age-rule.service';
import { IMSBookingModule } from 'vac-nest-ims-booking';
import { CDSEngineModule } from 'vac-nest-cds-engine';
import { MongooseModule } from '@nestjs/mongoose';
import { ValidateModel, ValidateSchema, SuggestModel, SuggestSchema, CheckModel, CheckSchema } from './models';
import { MongooseService } from './services/mongoose.service';
import { RuleMaxInjectionService } from './services/maxInjection-rule.service';
import { OsrModule } from 'vac-nest-osr';
import { SkuLimitRuleService } from './services/sku-limit-rule.service';
import { ScheduleCoreModule } from 'vac-nest-schedule';
import { OrderRuleEngineModule } from 'vac-nest-order-rule-engine';
import { VaccineConversionRuleService } from './services/vaccine-conversion-rule.service';
import { ScheduleSwitchRulesService } from './services/schedule-switch-rules.service';

@Module({
  imports: [
    IMSModule,
    FamilyModule,
    RegimenModule,
    PIMAppModule,
    VacHistoryModule,
    ExaminationCoreModule,
    IMSBookingModule,
    CDSEngineModule,
    OsrModule,
    MongooseModule.forFeature([
      { name: ValidateModel.name, schema: ValidateSchema },
      { name: SuggestModel.name, schema: SuggestSchema },
      { name: CheckModel.name, schema: CheckSchema },
    ]),
    ScheduleCoreModule,
    OrderRuleEngineModule,
  ],
  controllers: [SchedulesController],
  providers: [
    SchedulesService,
    ScheduleRulesService,
    MongooseService,
    RuleMaxInjectionService,
    AgeRuleService,
    SkuLimitRuleService,
    VaccineConversionRuleService,
    ScheduleSwitchRulesService,
  ],
  exports: [MongooseService],
})
export class SchedulesModule {}
