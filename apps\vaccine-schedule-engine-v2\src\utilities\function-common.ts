/* eslint-disable @typescript-eslint/no-inferrable-types */

import moment, { unitOfTime } from 'moment';
import { FORMAT_DATE, TIMEZONE } from '../constants';
import { Logger } from '@nestjs/common';

type Await<T> = T extends Promise<infer U> ? U : T;
type AwaitProps<T> = { [P in keyof T]: Await<T[P]> };

/**
 *
 * @param {Promise} ...args
 * @returns arrPromise
 */
export async function concurrentPromise<T extends Promise<any>[]>(...args: T): Promise<AwaitProps<T>> {
  const arrPromise = await Promise.allSettled(args);
  return arrPromise.map((entry) => {
    if (entry.status === 'fulfilled') {
      return entry.value;
    }
    return null;
  }) as AwaitProps<T>;
}

/**
 *
 * @param {Promise} ...args
 * @returns arrData
 */
export async function concurrentPromiseThrowError<T extends Promise<any>[]>(...args: T): Promise<AwaitProps<T>> {
  const arrPromise = await Promise.all(args);
  return arrPromise as AwaitProps<T>;
}

/**
 *
 * @param {Date} date format date
 * @param {String} timeZone default +00:00
 * @param {String} format default ""
 * @returns String
 */
export function parseDateTimeZone(date: Date, timeZone: '+00:00' | '+07:00', format: string = ''): string {
  return moment(date).utcOffset(timeZone).format(format);
}

export function getStartDate(date: Date, timeZone: '+00:00' | '+07:00', format: string = ''): string {
  return moment(moment(date).utcOffset(timeZone).startOf('D').format()).utcOffset(timeZone).format(format);
}

export function getStartDateV2(date: Date, timeZone: '+00:00' | '+07:00', format: string = ''): string {
  return moment(date).format(format);
}

export function isSameDate(dateA: Date, dateB: Date, timzeZone: string = '+07:00'): boolean {
  Logger.log(`DateA: ${moment(dateA).utcOffset(timzeZone).format('YYYY-MM-DD')}`);
  Logger.log(`DateB: ${moment(dateB).utcOffset(timzeZone).format('YYYY-MM-DD')}`);
  return moment(moment(dateA).utcOffset(timzeZone).format('YYYY-MM-DD')).isSame(
    moment(dateB).utcOffset(timzeZone).format('YYYY-MM-DD'),
    'D',
  );
}

/**
 * @description Tính khoảng cách
 * @param startDate Thời gian quá khứ
 * @param endDate THời gian tương lai
 * @param unit Khoảng cách D | M | Y
 * @returns Số khoảng cách
 */
export function diffDate(startDate: Date | string, endDate: Date | string, unit: unitOfTime.Diff): number {
  const startDateMoment = moment(parseDateTimeZone(new Date(startDate), TIMEZONE, FORMAT_DATE));
  Logger.log(`StartDate: ${startDateMoment.format()}`);
  const endDateMoment = moment(parseDateTimeZone(new Date(endDate), TIMEZONE, FORMAT_DATE));
  Logger.log(`EndDate: ${endDateMoment.format()}`);
  return endDateMoment.diff(startDateMoment, unit);
}

/**
 * @description Cộng thời gian
 * @param date Thời gian cần tính
 * @param count Số đơn vị cần thêm
 * @param unit loại đơn vị D | M | Y
 * @returns Date mới
 */
export function addDate(date: Date | string, count: number, unit: unitOfTime.Diff) {
  const dateMoment = moment(parseDateTimeZone(new Date(date), TIMEZONE, FORMAT_DATE));
  Logger.log(`Date: ${dateMoment.format()}`);
  return dateMoment.add(count, unit);
}

export function calculateTimeDifference(startDate: Date) {
  // startDate = new Date('2023-10-16T00:00:00Z');
  const startMoment = moment(moment(startDate).utcOffset(7).format(FORMAT_DATE)); // Chuyển ngày bắt đầu thành đối tượng Moment
  const currentMoment = moment(moment().utcOffset(7).format(FORMAT_DATE)); // Lấy ngày hiện tại

  const diffInDays = currentMoment.diff(startMoment, 'days'); // Số ngày khác biệt
  const diffInMonths = currentMoment.diff(startMoment, 'months'); // Số tháng khác biệt
  const diffInYears = currentMoment.diff(startMoment, 'years'); // Số năm khác biệt

  // Tính số ngày còn lại sau khi tính số năm, số tháng và số tuần
  const remainingDaysInYears = currentMoment.clone().subtract(diffInYears, 'years').diff(startMoment, 'days');
  const remainingDaysInMonths = currentMoment.clone().subtract(diffInMonths, 'months').diff(startMoment, 'days');
  const remainingDaysInWeeks = currentMoment.clone().subtract(diffInDays, 'days').diff(startMoment, 'days');

  // Trả về kết quả
  return {
    years: diffInYears,
    months: diffInMonths,
    weeks: diffInDays / 7,
    days: diffInDays,
    remainingDaysInYears: remainingDaysInYears,
    remainingDaysInMonths: remainingDaysInMonths,
    remainingDaysInWeeks: remainingDaysInWeeks,
  };
}

export function isBetweenNumber(value: number, min: number, max: number): boolean {
  return value >= min && value <= max;
}

export function isAfterDate(minDate: Date, date: Date, timeZone?: '+07:00'): boolean {
  return moment(moment(minDate).utcOffset(timeZone).format('YYYY-MM-DD'))?.isAfter(
    moment(date).utcOffset(7).format('YYYY-MM-DD'),
    'D',
  );
}

export function isSameOrAfterDate(minDate: Date, date: Date, timeZone?: '+07:00'): boolean {
  return moment(moment(minDate).utcOffset(timeZone).format('YYYY-MM-DD'))?.isSameOrAfter(
    moment(date).utcOffset(timeZone).format('YYYY-MM-DD'),
    'D',
  );
}
