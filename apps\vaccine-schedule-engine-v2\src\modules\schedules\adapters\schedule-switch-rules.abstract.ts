import { VaccineHistoryDetailDto } from 'vac-nest-history';
import { getRegimenSwitchRuleDto } from 'vac-nest-regimen';
import { RuleRes } from '../dto/common';
import { PreProcessCurrentDto, PreProcessFutureDto } from '../dto/pre-process-data.dto';

export abstract class ScheduleSwitchRuleAbstract {
  abstract checkScheduleSwitchRule(
    current: PreProcessCurrentDto,
    arrFuture: PreProcessFutureDto[],
    regimentIsClose: VaccineHistoryDetailDto[],
    rules: RuleRes[],
    switchRules: getRegimenSwitchRuleDto[],
  );
}
