import { HttpException } from '@nestjs/common';
import { ErrorCode } from '../errors/error-code';
import { IError } from '../interfaces';

export class SystemException extends HttpException {
  constructor(exceptionResponse: IError, statusCode: number) {
    super(
      HttpException.createBody({
        code: exceptionResponse?.code || ErrorCode.INTERNAL_SERVER,
        message: exceptionResponse?.message || ErrorCode.getError(ErrorCode.INTERNAL_SERVER),
        details: exceptionResponse?.details || ErrorCode.getError(ErrorCode.INTERNAL_SERVER),
        validationErrors: exceptionResponse?.validationErrors || null,
      }),
      statusCode,
    );
  }
}
