import { Prop, Schema } from '@nestjs/mongoose';
import { Expose } from 'class-transformer';
import mongoose from 'mongoose';

@Schema()
export class CommonModel {
  @Prop({ required: false, index: false, type: mongoose.Schema.Types.Mixed })
  @Expose()
  headers: any;

  @Prop({ required: false, index: false, type: mongoose.Schema.Types.String })
  @Expose()
  screenType: string;

  @Prop({ required: false, index: true, type: mongoose.Schema.Types.String })
  @Expose()
  ticketCode: number;

  @Prop({ required: false, index: true, type: mongoose.Schema.Types.String })
  @Expose()
  lcvId: string;

  @Prop({ required: false, index: true, type: mongoose.Schema.Types.Date })
  @Expose()
  createDate: Date;

  @Prop({ required: false, index: false, type: mongoose.Schema.Types.Date })
  @Expose()
  expectedDate: Date;

  @Prop({ required: false, index: false, type: mongoose.Schema.Types.Mixed })
  @Expose()
  payload: any;

  @Prop({ required: false, index: false, type: mongoose.Schema.Types.Mixed })
  @Expose()
  response: any;

  @Prop({ required: false, index: true, type: mongoose.Schema.Types.String })
  @Expose()
  'X-Request-ID': string;
}
