import { Body, Controller, HttpCode, HttpStatus, Param, Post, Put } from '@nestjs/common';
import { ApiBadRequestResponse, ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  GetScheduleByLcvIdDto,
  GetScheduleByPersonsDto,
  UpdateManyScheduleDto,
  UpdateScheduleById,
} from 'vac-nest-schedule';
import { Public } from '../../../common/decorators';
import { ClassErrorResponse, ClassResponse } from '../../../utilities/class-response';
import { generalSchema } from '../../../utilities/function-swagger';
import { GetInjectionScheduleVaccineDto, TransformInjectionSchedule } from '../dto';
import { InjectionScheduleVaccineService } from '../services/injection-schedule-vaccine.service';

@Controller({ path: 'injection-schedule-vaccine', version: '1' })
@ApiTags('Injection Schedule Vaccine')
// @ApiBearerAuth('defaultJWT')
@ApiExtraModels(ClassResponse, TransformInjectionSchedule)
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
export class InjectionScheduleVaccineController {
  constructor(private readonly injectionScheduleService: InjectionScheduleVaccineService) {}

  @Public()
  @Post('schedule')
  @ApiOperation({
    summary: '',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về lịch tiêm',
    schema: generalSchema(TransformInjectionSchedule, 'array'),
  })
  async injectionSchedule(@Body() body: GetInjectionScheduleVaccineDto) {
    return this.injectionScheduleService.getInjectionSchedule(body);
  }

  @Public()
  @Put('update-schedule/:id')
  @ApiOperation({
    summary: 'Cập nhật lịch tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về lịch tiêm',
    schema: generalSchema(TransformInjectionSchedule, 'array'),
  })
  async updateScheduleById(@Param('id') id: string, @Body() body: UpdateScheduleById) {
    return this.injectionScheduleService.updateScheduleById(id, body);
  }

  @Public()
  @Post('update-many-schedule')
  @ApiOperation({
    summary: 'Cập nhật nhiều lịch tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về lịch tiêm',
    schema: generalSchema(TransformInjectionSchedule, 'array'),
  })
  async updateManySchedule(@Body() body: UpdateManyScheduleDto) {
    return this.injectionScheduleService.updateManySchedule(body);
  }

  /** lấy danh sách lịch hẹn cho nhiều personId */
  @Public()
  @Post('schedule-vaccine-persons')
  @ApiOperation({
    summary: 'Lấy danh sách lịch tiêm cho cả người quan hệ',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về lịch tiêm cho cả người quan hệ',
    schema: generalSchema(TransformInjectionSchedule, 'array'),
  })
  async scheduleVaccinePersons(@Body() body: GetScheduleByPersonsDto) {
    return this.injectionScheduleService.getScheduleVaccinePersons(body);
  }

  /**
   * @TODO lấy danh sách lịch hẹn tiêm bởi lcvid
   */
  @Public()
  @Post('schedule-vaccine-by-lcvId')
  @ApiOperation({
    summary: 'Lấy danh sách lịch hẹn tiêm cho màn tư vấn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về lịch tiêm cho cả người quan hệ',
    schema: generalSchema(TransformInjectionSchedule, 'array'),
  })
  async scheduleVaccineLcvId(@Body() body: GetScheduleByLcvIdDto) {
    return this.injectionScheduleService.getListScheduleByLcvId(body);
  }
}
