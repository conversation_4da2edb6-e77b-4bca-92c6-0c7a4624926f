# FV-16789: Rule Giới Hạn <PERSON> (isRestrict) cho IVACFLU-S 0.5ML

## 1. Business Context

### 1.1. Hiện trạng

- Vắc xin IVACFLU-S 0.5ML chỉ có 1 phác đồ từ 18 - d<PERSON><PERSON><PERSON> 61 tuổi.

### 1.2. <PERSON><PERSON><PERSON> cầu

- C<PERSON><PERSON> nhật thành 3 phác đồ theo nhóm tuổi:
  - **Từ 06 tháng đến dưới 36 tháng**: li<PERSON>u tiêm 0,25ml
  - **Từ 36 tháng đến dưới 9 tuổi**: li<PERSON><PERSON> tiêm 0,5ml
  - **Từ 9 tuổi trở lên**: li<PERSON><PERSON> tiêm 0,5ml
- Thay thế phác đồ hiện tại chỉ áp dụng cho nhóm tuổi từ 18 đến dưới 61 tuổi.
- **<PERSON><PERSON><PERSON> <PERSON>ồ "Từ 06 tháng đến dưới 36 tháng" sẽ bị chặn ở màn hình lịch hẹn và chỉ định tiêm.**
- Chỉ mở khi có phê duyệt hội chẩn từ Hội đồng Y khoa.

### 1.3. Giải pháp

- **Regimen Core**: Thêm field mới `isRestrict` vào bảng Regimen. Nếu phác đồ cần chặn, set `isRestrict = true`.
- **Schedule Engine**:
  - Khi kiểm tra lịch tiêm, nếu phác đồ có `isRestrict = true` thì trả về rule chặn, yêu cầu hội chẩn.
  - Rule này áp dụng cho các API kiểm tra phác đồ (check) ở cả màn hình lịch hẹn và chỉ định tiêm.
  - Nếu HĐYK duyệt thì cho phép đi tiếp, không duyệt thì không được phép chỉ định tiêm.
- **RuleType**: Định nghĩa thêm giá trị `GioiHanPhacDo = 11` trong enum RuleType.

## 2. Use Case

- **Sale/Bác sĩ** tìm kiếm, thêm sản phẩm vào giỏ hàng, hệ thống gợi ý đúng phác đồ theo độ tuổi (3 phác đồ mới).
- **Sale/Bác sĩ** thêm lịch hẹn, chỉ định tiêm, hệ thống kiểm tra rule:
  - Nếu chọn phác đồ "Từ 06 tháng đến dưới 36 tháng" (isRestrict = true) thì chặn, yêu cầu hội chẩn.
  - Các phác đồ còn lại hoạt động bình thường.
- **Lịch sử tiêm, Vac order, Ticket**: Map đúng phác đồ với độ tuổi, migrate data nếu cần.

## 3. Rule Giới Hạn Phác Đồ (isRestrict)

- **ID (ruleType):** 11
- **Nội dung (ruleName):** Giới hạn phác đồ
- **Loại chặn:** DANGER
- **Chặn (type):** DANGER
- **isAllowEvaluate:** Có (cho phép hội chẩn ở cả màn hình lịch hẹn và chỉ định tiêm)
- **Thông báo:** "Phác đồ này bị giới hạn, vui lòng liên hệ bác sĩ để được tư vấn và hội chẩn!"

### Điều kiện áp dụng:

- Chỉ check với các lịch có status = 0 (mua), 1 (đã mua), 5 (thanh toán từng phần).
- Nếu `regimen.isRestrict === true` thì trả về rule chặn.

## 4. Vị trí Code & Giải thích

- **Định nghĩa RuleType:**
  - Thêm `GioiHanPhacDo = 11` vào enum RuleType tại `src/constants/app.constant.ts`.
- **Hàm kiểm tra rule:**
  - Thêm hàm `checkRuleRestrict(current, rules)` trong `src/modules/schedules/services/schedule-rules.service.ts`.
  - Hàm này sẽ push rule vào mảng rules nếu vi phạm.
- **Luồng gọi rule:**
  - Được gọi trong API checkAllSchedules (lịch hẹn, chỉ định tiêm) tại `src/modules/schedules/services/schedules.service.ts`.
  - Rule được check trước các rule khác để chặn sớm.

## 5. Test Case

- **Thêm vắc xin vào giỏ hàng cho khách chưa đủ tuổi:**
  - Cho phép thêm, gợi ý đúng phác đồ, nếu chọn phác đồ bị giới hạn thì chặn và yêu cầu hội chẩn.
- **Thêm lịch hẹn, chỉ định tiêm:**
  - Nếu chọn phác đồ bị giới hạn, hệ thống chặn và yêu cầu hội chẩn.
- **Lịch sử tiêm, Vac order, Ticket:**
  - Map đúng phác đồ với độ tuổi, migrate data nếu cần.
- **Sync với cổng TCQG:**
  - Đảm bảo mapping đúng phác đồ mới khi đồng bộ dữ liệu.

## 6. Lưu ý đặc biệt

- Khi cập nhật phác đồ mới, cần migrate data các bảng liên quan (lịch hẹn, lịch sử tiêm, vac order, ticket, ...).
- Đảm bảo mapping đúng phác đồ với độ tuổi khi đồng bộ hoặc truy xuất dữ liệu.
- Nếu phác đồ cũ bị inActive/Delete, cần migrate data sang phác đồ mới.

## 7. Tài liệu tham khảo

- BA: Đọc để hiểu nghiệp vụ, logic chặn, các trường hợp cần hội chẩn.
- Dev: Đọc để biết vị trí code, cách mở rộng rule, cách migrate data.
- Tester: Đọc để biết các case cần test, các trường hợp cần kiểm tra cảnh báo/chặn.

---

## 8. Visualization: Flow kiểm tra rule Giới hạn phác đồ

```mermaid
flowchart TD
    A["Bắt đầu kiểm tra lịch tiêm"] --> B{"status = 0, 1, 5?"}
    B -- "Không" --> Z["Không check rule Giới hạn phác đồ"]
    B -- "Có" --> C{"regimen.isRestrict = true?"}
    C -- "Không" --> Z
    C -- "Có" --> D["Push rule chặn: Giới hạn phác đồ, yêu cầu hội chẩn"]
    D --> E{"HĐYK duyệt?"}
    E -- "Không" --> F["Chặn, không cho phép chỉ định tiêm"]
    E -- "Có" --> G["Cho phép tiếp tục quy trình"]
    Z --> H["Kết thúc"]
    F --> H
    G --> H
```

---

## 9. Code Implementation

### 9.1. Định nghĩa enum RuleType

```ts
// src/constants/app.constant.ts
export enum RuleType {
  DenSom = 0,
  ChuaDuTuoi = 1,
  QuaTuoi = 2,
  HaiTiemMotUong = 3,
  TuongTac = 4,
  HaiMuiCungNhomBenh = 5,
  HetTon = 6,
  QuaMuiToiDa = 8,
  HetTonHangKhangHiem = 9,
  ChuyenDoiVacXin = 10,
  GioiHanPhacDo = 11, // <--- Thêm mới
}
```

### 9.2. Hàm kiểm tra rule Giới hạn phác đồ

```ts
// src/modules/schedules/services/schedule-rules.service.ts
checkRuleRestrict(current: PreProcessCurrentDto, rules: RuleRes[]) {
  // Chỉ check với status = 0 (mua), 1 (đã mua), 5 (thanh toán từng phần)
  if (![0, 1, 5].includes(current?.status)) return false;
  if (!current?.regimen?.isRestrict) return false;
  rules.push({
    text: `Trẻ từ 06 tháng đến dưới 36 tháng thuộc nhóm tuổi cần đánh giá chỉ định tiêm. Vui lòng hội chẩn trước khi tiếp tục tiêm phác đồ này`,
    type: 'DANGER',
    value: 0,
    ruleType: RuleType.GioiHanPhacDo,
    ruleName: 'Giới hạn phác đồ',
    isAllowEvaluate: true,
  });
  return true;
}
```

### 9.3. Vị trí gọi rule trong luồng checkAllSchedules

```ts
// src/modules/schedules/services/schedules.service.ts
_.forEach(arrCurrent, (current: PreProcessCurrentDto) => {
  if (current?.date && current?.status !== 2) {
    const rules: RuleRes[] = [];
    // ...
    this.scheduleRulesService.checkRuleRestrict(current, rules);
    // ...
  }
});
```

---
