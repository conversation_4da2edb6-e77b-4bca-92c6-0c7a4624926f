import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { VaccineConversionRuleService } from './vaccine-conversion-rule.service';
import { EnumDistanceType, RuleType } from '../../../constants';

describe('VaccineConversionRuleService', () => {
  let service: VaccineConversionRuleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VaccineConversionRuleService,
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<VaccineConversionRuleService>(VaccineConversionRuleService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return false when there are no conversion rules for the current SKU', () => {
    const date = new Date();
    const current = { sku: 'SKU1', screenType: 'INDICATION', regimen: { vaccine: { name: 'Vaccine A' } } } as any;
    const arrFuture = [];
    const extra = { vaccineConversion: {} };
    const rules = [];

    const result = service.checkVaccineConversionRule(date, current, arrFuture, extra, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });

  it('should return false when there are conversion rules but no matching future vaccines', () => {
    const date = new Date();
    const current = { sku: 'SKU1', screenType: 'INDICATION', regimen: { vaccine: { name: 'Vaccine A' } } } as any;
    const arrFuture = [{ sku: 'SKU3', date: new Date(), regimen: { vaccine: { name: 'Vaccine C' } } }] as any;
    const extra = {
      vaccineConversion: {
        SKU1: [
          {
            Sku1: 'SKU1',
            Sku2: 'SKU2',
            OrderInjection1: 1,
            OrderInjection2: 1,
            MinDistanceType: EnumDistanceType.days,
            MinDistanceValue: 28,
            WarningType: 2,
          },
        ],
      },
    } as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(date, current, arrFuture, extra, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });

  it('should return false when there are matching future vaccines but no applicable rule', () => {
    const date = new Date();
    const current = { sku: 'SKU1', screenType: 'INDICATION', regimen: { vaccine: { name: 'Vaccine A' } } } as any;
    const arrFuture = [{ sku: 'SKU2', date: new Date(), regimen: { vaccine: { name: 'Vaccine B' } } }] as any;
    const extra = {
      vaccineConversion: {
        SKU1: [
          // OrderInjection1 doesn't match (2 vs 1)
          {
            Sku1: 'SKU1',
            Sku2: 'SKU2',
            OrderInjection1: 2,
            OrderInjection2: 1,
            MinDistanceType: EnumDistanceType.days,
            MinDistanceValue: 28,
            WarningType: 2,
          },
        ],
      },
    } as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(date, current, arrFuture, extra, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });

  it('should return true and add a rule when minimum distance is not met', () => {
    const today = new Date();
    const tenDaysAgo = new Date(today);
    tenDaysAgo.setDate(today.getDate() - 10);

    const current = {
      sku: 'SKU1',
      screenType: 'INDICATION',
      regimen: { vaccine: { name: 'Vaccine A' } },
    } as any;
    const arrFuture = [{ sku: 'SKU2', date: tenDaysAgo, regimen: { vaccine: { name: 'Vaccine B' } } }] as any;
    const extra = {
      vaccineConversion: {
        SKU1: [
          {
            Sku1: 'SKU1',
            Sku2: 'SKU2',
            OrderInjection1: 1,
            OrderInjection2: 1,
            MinDistanceType: EnumDistanceType.days,
            MinDistanceValue: 28,
            WarningType: 2,
          },
        ],
      },
    } as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(today, current, arrFuture, extra, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
    expect(rules[0].ruleType).toBe(RuleType.ChuyenDoiVacXin);
    expect(rules[0].type).toBe('DANGER'); // WarningType 2 should be DANGER
  });

  it('should return false when minimum distance is met', () => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const current = {
      sku: 'SKU1',
      screenType: 'INDICATION',
      regimen: { vaccine: { name: 'Vaccine A' } },
    } as any;
    const arrFuture = [{ sku: 'SKU2', date: thirtyDaysAgo, regimen: { vaccine: { name: 'Vaccine B' } } }] as any;
    const extra = {
      vaccineConversion: {
        SKU1: [
          {
            Sku1: 'SKU1',
            Sku2: 'SKU2',
            OrderInjection1: 1,
            OrderInjection2: 1,
            MinDistanceType: EnumDistanceType.days,
            MinDistanceValue: 28,
            WarningType: 2,
          },
        ],
      },
    } as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(today, current, arrFuture, extra, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });

  it('should handle different distance types correctly (months)', () => {
    const today = new Date('2023-01-01');
    const sixMonthsAgo = new Date('2022-07-01');

    const current = {
      sku: 'SKU1',
      screenType: 'INDICATION',
      regimen: { vaccine: { name: 'Vaccine A' } },
    } as any;
    const arrFuture = [{ sku: 'SKU2', date: sixMonthsAgo, regimen: { vaccine: { name: 'Vaccine B' } } }] as any;
    const extra = {
      vaccineConversion: {
        SKU1: [
          {
            Sku1: 'SKU1',
            Sku2: 'SKU2',
            OrderInjection1: 1,
            OrderInjection2: 1,
            MinDistanceType: EnumDistanceType.months,
            MinDistanceValue: 8,
            WarningType: 1,
          },
        ],
      },
    } as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(today, current, arrFuture, extra, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
    expect(rules[0].type).toBe('WARNING'); // WarningType 1 should be WARNING
  });

  it('should handle different distance types correctly (years)', () => {
    const today = new Date('2023-01-01');
    const oneYearAgo = new Date('2022-01-01');

    const current = {
      sku: 'SKU1',
      screenType: 'INDICATION',
      regimen: { vaccine: { name: 'Vaccine A' } },
    } as any;
    const arrFuture = [{ sku: 'SKU2', date: oneYearAgo, regimen: { vaccine: { name: 'Vaccine B' } } }] as any;
    const extra = {
      vaccineConversion: {
        SKU1: [
          {
            Sku1: 'SKU1',
            Sku2: 'SKU2',
            OrderInjection1: 1,
            OrderInjection2: 1,
            MinDistanceType: EnumDistanceType.years,
            MinDistanceValue: 2,
            WarningType: 1,
          },
        ],
      },
    } as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(today, current, arrFuture, extra, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
  });

  it('should handle multiple instances of the same future SKU correctly', () => {
    const today = new Date();
    const tenDaysAgo = new Date(today);
    tenDaysAgo.setDate(today.getDate() - 10);
    const twentyDaysAgo = new Date(today);
    twentyDaysAgo.setDate(today.getDate() - 20);

    const current = {
      sku: 'SKU1',
      screenType: 'INDICATION',
      regimen: { vaccine: { name: 'Vaccine A' } },
    } as any;
    const arrFuture = [
      { sku: 'SKU2', date: tenDaysAgo, regimen: { vaccine: { name: 'Vaccine B' } } },
      { sku: 'SKU2', date: twentyDaysAgo, regimen: { vaccine: { name: 'Vaccine B' } } },
    ] as any;
    const extra = {
      vaccineConversion: {
        SKU1: [
          {
            Sku1: 'SKU1',
            Sku2: 'SKU2',
            OrderInjection1: 1,
            OrderInjection2: 2,
            MinDistanceType: EnumDistanceType.days,
            MinDistanceValue: 28,
            WarningType: 2,
          },
        ],
      },
    } as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(today, current, arrFuture, extra, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
  });

  it('should handle null/undefined values gracefully', () => {
    const date = new Date();
    const current = { sku: 'SKU1', screenType: 'INDICATION', regimen: { vaccine: { name: 'Vaccine A' } } } as any;
    const arrFuture = [] as any;
    const extra = null as any;
    const rules = [];

    const result = service.checkVaccineConversionRule(date, current, arrFuture, extra, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });
});
