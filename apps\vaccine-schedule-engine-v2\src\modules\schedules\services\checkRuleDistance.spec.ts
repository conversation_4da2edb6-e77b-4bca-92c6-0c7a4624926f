import { Test, TestingModule } from '@nestjs/testing';
import { ScheduleRulesService } from './schedule-rules.service';
import { RuleRes } from '../dto/common';
import { AgeRuleService } from './age-rule.service';

describe('ScheduleRulesService', () => {
  let scheduleRulesService: ScheduleRulesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleRulesService,
        {
          provide: 'ScheduleRulesService',
          useClass: ScheduleRulesService,
        },
        AgeRuleService,
        {
          provide: 'AgeRuleService',
          useClass: AgeRuleService,
        },
      ],
    }).compile();

    scheduleRulesService = module.get(ScheduleRulesService);
  });

  it('ScheduleRulesService should be defined', () => {
    expect(scheduleRulesService).toBeDefined();
  });

  describe('checkRuleDistance', () => {
    const date = new Date();
    const rules: RuleRes[] = [];
    const current: any = {
      regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
      isChangeDate: true,
      lcvId: 'LP580021737515618249',
      sku: '00038149',
      date: '2025-01-22T15:35:07+07:00',
      status: 0,
      orderInjections: 3,
      priority: 3,
      diseaseGroupId: '9b513cb2-016d-426b-a5fb-786166da7751',
      vaccinatedNow: 'NOW',
      orderDetailAttachmentCode: '',
      orderDetailAttachmentId: null,
      orderInjectionId: '',
      sourceId: 0,
      vacOrderCode: '',
      orderCode: '',
      ticketCode: '',
      oldDate: '2025-01-22T15:35:07+07:00',
      unitCode: 15,
      unitName: 'Ống',
      dosage: '0.5 ml',
      injectionRoute: 'Tiêm bắp',
      extraData: {
        unitCodeSale: 15,
        unitNameSale: 'Ống',
      },
      rules: [],
      screenType: 'INDICATION',
      regimen: {
        id: '2a492040-653a-403f-96c1-26efeac3efe9',
        priority: 3,
        matchingScore: 1,
        unitCode: 0,
        regimenType: 1,
        scheduleType: 'Từ 9 tuổi',
        vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
        ageUnit: 'Age',
        fromAge: '9 tuổi',
        toAge: '',
        ageUnitCode: 3,
        from: 9,
        to: 1000,
        ageFromUnitCode: 3,
        ageFromValue: 9,
        ageToUnitCode: 3,
        ageToValue: 1000,
        equalToAge: true,
        equalFromAge: true,
        requiredInjections: 1,
        details: [
          {
            regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
            order: 1,
            nearestInjectionDistance: '0',
            nearestInjectionDistanceValue: 0,
            distance: 0,
            isRequired: true,
            dosage: 0.5,
            unit: 'ml',
            minDistance: 0,
            maxDistance: 0,
            distanceValueByType: 0,
            distanceType: 1,
            dayAllowEarly: 60,
            regimenRules: [
              {
                id: '926923c4-3b20-47e8-a7ba-947d926a4435',
                regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                regimenDetailId: 'd44cf0f7-1144-40fc-bf28-32173db0586e',
                sku: '00038149',
                from: 1,
                fromUnit: 0,
                to: 0,
                toUnit: 0,
                equalFrom: true,
                equalTo: true,
                regimenRulesType: 2,
                alertLevel: 2,
                createdDate: '2024-12-19T16:53:21.826344',
                createdBy: 'system',
                modifiedDate: '2024-12-19T16:53:21.826344',
                modifiedBy: 'system',
                isActive: true,
                isDeleted: false,
              },
            ],
            ageFromUnitCode: 3,
            ageFromValue: 9,
            ageToUnitCode: 3,
            ageToValue: 1000,
            equalFromAge: true,
            equalToAge: true,
            isActive: true,
          },
          {
            regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
            order: 2,
            nearestInjectionDistance: '30 x 12',
            nearestInjectionDistanceValue: 360,
            distance: 360,
            isRequired: false,
            repeatFrequency: '01 NĂM',
            dosage: 0.5,
            unit: 'ml',
            minDistance: 358,
            maxDistance: 360,
            distanceValueByType: 12,
            distanceType: 1,
            dayAllowEarly: 60,
            regimenRules: [
              {
                id: '04ea4c77-6816-43e9-89f7-87e9963f450a',
                regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                sku: '00038149',
                from: 1,
                fromUnit: 0,
                to: 0,
                toUnit: 0,
                equalFrom: true,
                equalTo: true,
                regimenRulesType: 2,
                alertLevel: 2,
                createdDate: '2024-12-19T16:53:21.826344',
                createdBy: 'system',
                modifiedDate: '2024-12-19T16:53:21.826344',
                modifiedBy: 'system',
                isActive: true,
                isDeleted: false,
              },
              {
                id: '4c335a96-d428-4835-98f5-fb12046496b2',
                regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                sku: '00038149',
                from: 30,
                fromUnit: 0,
                to: 240,
                toUnit: 0,
                equalFrom: true,
                equalTo: true,
                regimenRulesType: 1,
                alertLevel: 1,
                createdDate: '2024-08-15T13:54:08.658296',
                createdBy: 'system',
                modifiedDate: null,
                modifiedBy: '',
                isActive: true,
                isDeleted: false,
              },
              {
                id: '1d8d97f3-75c0-4c90-bd30-c45c4d0e73f3',
                regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                sku: '00038149',
                from: 241,
                fromUnit: 0,
                to: 0,
                toUnit: 0,
                equalFrom: true,
                equalTo: true,
                regimenRulesType: 1,
                alertLevel: 2,
                createdDate: '2024-08-15T13:54:08.658296',
                createdBy: 'system',
                modifiedDate: null,
                modifiedBy: '',
                isActive: true,
                isDeleted: false,
              },
            ],
            ageFromUnitCode: 3,
            ageFromValue: 9,
            ageToUnitCode: 3,
            ageToValue: 1000,
            equalFromAge: true,
            equalToAge: true,
            isActive: true,
          },
        ],
        isDefault: true,
        baseScore: 0,
        note: 'Mũi 1: Lần tiêm đầu tiên\nMũi nhắc: 01 mũi nhắc hàng năm.',
        comboType: 0,
        vaccine: {
          id: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
          sku: '00038149',
          name: 'INFLUVAC TETRA',
          type: 'Bất hoạt',
          typeCode: 2,
          pregnantNoteCode: 0,
          genderName: 'Tất cả',
          gender: -1,
          pregnantNote: 'Không có',
          note: '',
          preinjectionTest: false,
          minAge: 'Từ 6 tháng',
          maxAge: '',
          manufactor: 'Hà Lan',
          producer: '',
          brand: 'Abbott',
          brandOrigin: 'Hoa Kỳ',
          vaccineDiseases: [
            {
              vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
              diseaseId: '0e4d6d13-1b97-4f6d-ac44-d3e05ce17153',
              diseaseName: 'CÚM',
              id: '13d8e855-4613-4f54-bf21-c91a0eba0898',
              createdDate: '2023-12-02T17:41:22.9096370',
            },
          ],
          injectionRoutes: [],
          indicationNotes: [
            {
              contraindications:
                'Các lưu ý khác: \n- CCĐ: Quá mẫn với thành phần trứng, formaldehyde, cetyltrimethylamoni bromid, polysorbat 80, gentamicin (thành phần của vắc xin Influvac Tetra)',
              precautions: '',
              otherNote: '',
              postponement: '',
              transfer2Hospital: '',
            },
          ],
          vaccineInteractions: [
            {
              vaccineId: 'e1206ca5-58ba-4e04-b27a-47a80db573ef',
              sku: '00045856',
              name: 'QDENGA',
              minDistance: '1 ngày',
              minDistanceValue: 1,
              note: 'Vaccine QDENGA chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
              warningType: 0,
              warningContent:
                'Vaccine QDENGA chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
              isInjectionSameDay: false,
              interactionType: 4,
            },
            {
              vaccineId: 'b046a06e-cfc1-4327-9bfe-8efd07372dbd',
              sku: '00043237',
              name: 'BEXSERO',
              minDistance: '1 ngày',
              minDistanceValue: 1,
              note: 'Vaccine BEXSERO chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
              warningType: 0,
              warningContent:
                'Vaccine BEXSERO chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
              isInjectionSameDay: false,
              interactionType: 4,
            },
            {
              vaccineId: '9fc2c76a-531a-458f-9703-3fd9c5f8a15f',
              sku: '00038255',
              name: 'VAXIGRIP TETRA',
              minDistance: '28 ngày',
              minDistanceValue: 28,
              note: 'Vắc xin có cùng thành phần nên tiêm cách nhau 28 ngày',
              warningType: 0,
              warningContent:
                'Tương tác vắc xin cùng thành phần: INFLUVAC TETRA và VAXIGRIP TETRA nên tiêm cách nhau 28 ngày',
              isInjectionSameDay: false,
              interactionType: 1,
            },
            {
              vaccineId: 'c85f8ba6-bb27-4deb-a834-1c4466d83113',
              sku: '00038218',
              name: 'GCFLU QUADRIVALENT',
              minDistance: '28 ngày',
              minDistanceValue: 28,
              note: 'Vắc xin có cùng thành phần nên tiêm cách nhau 28 ngày',
              warningType: 0,
              warningContent:
                'Tương tác vắc xin cùng thành phần: INFLUVAC TETRA và GCFLU QUADRIVALENT nên tiêm cách nhau 28 ngày',
              isInjectionSameDay: false,
              interactionType: 1,
            },
          ],
          vaccineReplacements: [],
          prices: [
            {
              id: 185072,
              sku: '00038149',
              priceBookId: 1,
              productMeasureUnitId: 63758,
              level: 1,
              price: 334000,
              measureUnitId: 1,
              measureUnitName: 'Hộp',
              fromDate: '2024-11-26T00:00:00',
              toDate: '2050-12-31T00:00:00',
              approvedDate: '2024-11-26T08:25:21',
            },
            {
              id: 185073,
              sku: '00038149',
              priceBookId: 1,
              productMeasureUnitId: 63739,
              level: 3,
              price: 334000,
              measureUnitId: 15,
              measureUnitName: 'Ống',
              fromDate: '2024-11-26T00:00:00',
              toDate: '2050-12-31T00:00:00',
              approvedDate: '2024-11-26T08:25:21',
            },
          ],
          categories: [
            {
              level: 1,
              id: 1734702,
              categoryName: 'VACCINE',
              categoryId: 'ac3a2499-64e9-46f2-acfa-3a0ca9153694',
              uniqueId: 34,
            },
            {
              level: 2,
              id: 1734703,
              categoryName: 'CÚM',
              categoryId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
              uniqueId: 3881,
            },
          ],
          measures: [
            {
              isSellDefault: true,
              measureRateId: 28,
              measureUnitName: 'Ống',
              measureUnitId: 15,
              isDefault: true,
              measureRateName: 'Chuyển đổi Ống sang 1xỐng',
              level: 3,
              id: 63739,
              ratio: 1,
              uniqueId: 15,
              price: 334000,
            },
            {
              isSellDefault: false,
              measureRateId: 884,
              measureUnitName: 'Hộp',
              measureUnitId: 1,
              isDefault: false,
              measureRateName: 'Chuyển đổi Hộp sang 1 Ống',
              level: 1,
              id: 63758,
              ratio: 1,
              uniqueId: 1,
              price: 334000,
            },
          ],
          taxonomies: [
            {
              id: 106802,
              taxonomyId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
              taxonomyName: 'CÚM',
              level: 3,
            },
            {
              id: 110359,
              taxonomyId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
              taxonomyName: 'CÚM',
              level: 3,
            },
          ],
          requireBeforeInjection: 0,
          isMultiDose: false,
        },
        diseaseGroupId: '9b513cb2-016d-426b-a5fb-786166da7751',
        diseaseGroup: {
          name: 'CÚM',
          normalizeName: 'cum',
          disease: 'CÚM',
          engName: 'Influenza ',
          viName: 'Cúm',
        },
        isPregnantRegimen: false,
        injectionRoutes: [
          {
            vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
            usage: 'Tiêm bắp',
            isDefault: true,
            position: '',
            regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
            id: 'bb6850b4-f411-44d8-b813-155f01135e78',
            createdDate: '2023-08-16T11:20:06.3286250',
          },
        ],
      },
      stock: {
        sku: '00038149',
        quantity: 100193,
        quantityAvailable: -99803,
        quantityOrder: 199996,
        whsCode: '58005010',
        whsName: 'VX005-Kho hàng thường',
        unitCode: 15,
        unitName: 'Ống',
        unitLevel: 3,
        quantityExchange: 1,
      },
      person: {
        lcvId: 'LP580021737515618249',
        customerId: 'efb5b104-6661-3e9f-fa7b-3a179e2d2852',
        name: 'Dx. Nguyên Văn Suggest',
        nationalVaccineCode: null,
        nationalVaccineId: null,
        identityCard: null,
        dateOfBirth: '1999-11-11T12:00:00Z',
        gender: 0,
        phoneNumber: '0135792468',
        jobTitle: null,
        email: null,
        frequentlyProvinceCode: '23',
        frequentlyProvinceName: 'Hồ Chí Minh',
        frequentlyDistrictCode: '238',
        frequentlyDistrictName: 'Huyện Bình Chánh',
        frequentlyWardCode: '27595',
        frequentlyWardName: 'Thị trấn Tân Túc',
        frequentlyAddress: '1234',
        temporaryProvinceCode: '23',
        temporaryProvinceName: 'Hồ Chí Minh',
        temporaryDistrictCode: '238',
        temporaryDistrictName: 'Huyện Bình Chánh',
        temporaryWardCode: '27595',
        temporaryWardName: 'Thị trấn Tân Túc',
        temporaryAddress: '1234',
        ethnicCode: '01',
        ethnicName: null,
        isHost: true,
        guardianName: 'Dx. Check',
        guardianPhone: '0135798642',
        avatarUrl: null,
        isSameAddress: true,
        isHaveGuardian: true,
        note: null,
        familyProfileId: '3a179e2d-2a88-a395-0c89-4fb2f63dcdb4',
        titleName: null,
        lastSyncTcqg: null,
        status: 0,
        referralSourceId: null,
        referralSourceNote: null,
        source: 0,
        isActive: true,
        nonPhoneCustomer: false,
        from: 0,
        to: 0,
        ageUnitCode: 0,
        ageUnit: null,
        nationalityCode: null,
        nationalityName: null,
        id: '3a179e2d-291f-17f8-2b7a-106ce93c6307',
        lcvIdDisplayName: 'LP515618249',
        pregnancy: [],
        familyProfileDetails: [
          {
            lcvId: 'LP580021737515618249',
            titleId: '8d54cabe-f239-4c9a-b5de-4d9f8727ce7d',
            name: 'Dx. Nguyên Văn Suggest',
            nationalVaccineCode: null,
            nationalVaccineId: null,
            dateOfBirth: '1999-11-11T12:00:00Z',
            customerId: 'efb5b104-6661-3e9f-fa7b-3a179e2d2852',
            gender: 0,
            phoneNumber: '0135792468',
            identityCard: null,
            familyProfileId: '3a179e2d-2a88-a395-0c89-4fb2f63dcdb4',
            titleName: 'Khác',
            from: 0,
            to: 0,
            ageUnitCode: 0,
            ageUnit: null,
            personId: '3a179e2d-291f-17f8-2b7a-106ce93c6307',
            isHost: true,
            isNewInsert: false,
          },
          {
            lcvId: 'LP580021737515618657',
            titleId: '89f64e68-91c1-4b8b-8f02-5dd1679cd63b',
            name: 'Dx. Check',
            nationalVaccineCode: null,
            nationalVaccineId: null,
            dateOfBirth: null,
            customerId: 'ef3333d4-19e1-ceeb-1275-3a179e2d29b9',
            gender: 1,
            phoneNumber: '0135798642',
            identityCard: null,
            familyProfileId: '3a179e2d-2a88-a395-0c89-4fb2f63dcdb4',
            titleName: 'Con trai',
            from: 0,
            to: 0,
            ageUnitCode: 0,
            ageUnit: null,
            personId: '3a179e2d-2a46-2483-0e74-14401e6eb496',
            isHost: false,
            isNewInsert: false,
          },
        ],
      },
      totalInjectionClose: 0,
      totalHistory: 2,
    };
    const arrFuture: any = [
      {
        date: '2023-12-01T12:00:00+07:00',
        regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
        sku: '00038149',
        status: 2,
        diseaseGroupId: '9b513cb2-016d-426b-a5fb-786166da7751',
        orderInjections: 1,
        regimen: {
          id: '2a492040-653a-403f-96c1-26efeac3efe9',
          priority: 3,
          matchingScore: 1,
          unitCode: 0,
          regimenType: 1,
          scheduleType: 'Từ 9 tuổi',
          vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
          ageUnit: 'Age',
          fromAge: '9 tuổi',
          toAge: '',
          ageUnitCode: 3,
          from: 9,
          to: 1000,
          ageFromUnitCode: 3,
          ageFromValue: 9,
          ageToUnitCode: 3,
          ageToValue: 1000,
          equalToAge: true,
          equalFromAge: true,
          requiredInjections: 1,
          details: [
            {
              regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
              order: 1,
              nearestInjectionDistance: '0',
              nearestInjectionDistanceValue: 0,
              distance: 0,
              isRequired: true,
              dosage: 0.5,
              unit: 'ml',
              minDistance: 0,
              maxDistance: 0,
              distanceValueByType: 0,
              distanceType: 1,
              dayAllowEarly: 60,
              regimenRules: [
                {
                  id: '926923c4-3b20-47e8-a7ba-947d926a4435',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'd44cf0f7-1144-40fc-bf28-32173db0586e',
                  sku: '00038149',
                  from: 1,
                  fromUnit: 0,
                  to: 0,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 2,
                  alertLevel: 2,
                  createdDate: '2024-12-19T16:53:21.826344',
                  createdBy: 'system',
                  modifiedDate: '2024-12-19T16:53:21.826344',
                  modifiedBy: 'system',
                  isActive: true,
                  isDeleted: false,
                },
              ],
              ageFromUnitCode: 3,
              ageFromValue: 9,
              ageToUnitCode: 3,
              ageToValue: 1000,
              equalFromAge: true,
              equalToAge: true,
              isActive: true,
            },
            {
              regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
              order: 2,
              nearestInjectionDistance: '30 x 12',
              nearestInjectionDistanceValue: 360,
              distance: 360,
              isRequired: false,
              repeatFrequency: '01 NĂM',
              dosage: 0.5,
              unit: 'ml',
              minDistance: 358,
              maxDistance: 360,
              distanceValueByType: 12,
              distanceType: 1,
              dayAllowEarly: 60,
              regimenRules: [
                {
                  id: '04ea4c77-6816-43e9-89f7-87e9963f450a',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                  sku: '00038149',
                  from: 1,
                  fromUnit: 0,
                  to: 0,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 2,
                  alertLevel: 2,
                  createdDate: '2024-12-19T16:53:21.826344',
                  createdBy: 'system',
                  modifiedDate: '2024-12-19T16:53:21.826344',
                  modifiedBy: 'system',
                  isActive: true,
                  isDeleted: false,
                },
                {
                  id: '4c335a96-d428-4835-98f5-fb12046496b2',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                  sku: '00038149',
                  from: 30,
                  fromUnit: 0,
                  to: 240,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 1,
                  alertLevel: 1,
                  createdDate: '2024-08-15T13:54:08.658296',
                  createdBy: 'system',
                  modifiedDate: null,
                  modifiedBy: '',
                  isActive: true,
                  isDeleted: false,
                },
                {
                  id: '1d8d97f3-75c0-4c90-bd30-c45c4d0e73f3',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                  sku: '00038149',
                  from: 241,
                  fromUnit: 0,
                  to: 0,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 1,
                  alertLevel: 2,
                  createdDate: '2024-08-15T13:54:08.658296',
                  createdBy: 'system',
                  modifiedDate: null,
                  modifiedBy: '',
                  isActive: true,
                  isDeleted: false,
                },
              ],
              ageFromUnitCode: 3,
              ageFromValue: 9,
              ageToUnitCode: 3,
              ageToValue: 1000,
              equalFromAge: true,
              equalToAge: true,
              isActive: true,
            },
          ],
          isDefault: true,
          baseScore: 0,
          note: 'Mũi 1: Lần tiêm đầu tiên\nMũi nhắc: 01 mũi nhắc hàng năm.',
          comboType: 0,
          vaccine: {
            id: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
            sku: '00038149',
            name: 'INFLUVAC TETRA',
            type: 'Bất hoạt',
            typeCode: 2,
            pregnantNoteCode: 0,
            genderName: 'Tất cả',
            gender: -1,
            pregnantNote: 'Không có',
            note: '',
            preinjectionTest: false,
            minAge: 'Từ 6 tháng',
            maxAge: '',
            manufactor: 'Hà Lan',
            producer: '',
            brand: 'Abbott',
            brandOrigin: 'Hoa Kỳ',
            vaccineDiseases: [
              {
                vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
                diseaseId: '0e4d6d13-1b97-4f6d-ac44-d3e05ce17153',
                diseaseName: 'CÚM',
                id: '13d8e855-4613-4f54-bf21-c91a0eba0898',
                createdDate: '2023-12-02T17:41:22.9096370',
              },
            ],
            injectionRoutes: [],
            indicationNotes: [
              {
                contraindications:
                  'Các lưu ý khác: \n- CCĐ: Quá mẫn với thành phần trứng, formaldehyde, cetyltrimethylamoni bromid, polysorbat 80, gentamicin (thành phần của vắc xin Influvac Tetra)',
                precautions: '',
                otherNote: '',
                postponement: '',
                transfer2Hospital: '',
              },
            ],
            vaccineInteractions: [
              {
                vaccineId: 'e1206ca5-58ba-4e04-b27a-47a80db573ef',
                sku: '00045856',
                name: 'QDENGA',
                minDistance: '1 ngày',
                minDistanceValue: 1,
                note: 'Vaccine QDENGA chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                warningType: 0,
                warningContent:
                  'Vaccine QDENGA chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                isInjectionSameDay: false,
                interactionType: 4,
              },
              {
                vaccineId: 'b046a06e-cfc1-4327-9bfe-8efd07372dbd',
                sku: '00043237',
                name: 'BEXSERO',
                minDistance: '1 ngày',
                minDistanceValue: 1,
                note: 'Vaccine BEXSERO chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                warningType: 0,
                warningContent:
                  'Vaccine BEXSERO chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                isInjectionSameDay: false,
                interactionType: 4,
              },
              {
                vaccineId: '9fc2c76a-531a-458f-9703-3fd9c5f8a15f',
                sku: '00038255',
                name: 'VAXIGRIP TETRA',
                minDistance: '28 ngày',
                minDistanceValue: 28,
                note: 'Vắc xin có cùng thành phần nên tiêm cách nhau 28 ngày',
                warningType: 0,
                warningContent:
                  'Tương tác vắc xin cùng thành phần: INFLUVAC TETRA và VAXIGRIP TETRA nên tiêm cách nhau 28 ngày',
                isInjectionSameDay: false,
                interactionType: 1,
              },
              {
                vaccineId: 'c85f8ba6-bb27-4deb-a834-1c4466d83113',
                sku: '00038218',
                name: 'GCFLU QUADRIVALENT',
                minDistance: '28 ngày',
                minDistanceValue: 28,
                note: 'Vắc xin có cùng thành phần nên tiêm cách nhau 28 ngày',
                warningType: 0,
                warningContent:
                  'Tương tác vắc xin cùng thành phần: INFLUVAC TETRA và GCFLU QUADRIVALENT nên tiêm cách nhau 28 ngày',
                isInjectionSameDay: false,
                interactionType: 1,
              },
            ],
            vaccineReplacements: [],
            prices: [
              {
                id: 185072,
                sku: '00038149',
                priceBookId: 1,
                productMeasureUnitId: 63758,
                level: 1,
                price: 334000,
                measureUnitId: 1,
                measureUnitName: 'Hộp',
                fromDate: '2024-11-26T00:00:00',
                toDate: '2050-12-31T00:00:00',
                approvedDate: '2024-11-26T08:25:21',
              },
              {
                id: 185073,
                sku: '00038149',
                priceBookId: 1,
                productMeasureUnitId: 63739,
                level: 3,
                price: 334000,
                measureUnitId: 15,
                measureUnitName: 'Ống',
                fromDate: '2024-11-26T00:00:00',
                toDate: '2050-12-31T00:00:00',
                approvedDate: '2024-11-26T08:25:21',
              },
            ],
            categories: [
              {
                level: 1,
                id: 1734702,
                categoryName: 'VACCINE',
                categoryId: 'ac3a2499-64e9-46f2-acfa-3a0ca9153694',
                uniqueId: 34,
              },
              {
                level: 2,
                id: 1734703,
                categoryName: 'CÚM',
                categoryId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
                uniqueId: 3881,
              },
            ],
            measures: [
              {
                isSellDefault: true,
                measureRateId: 28,
                measureUnitName: 'Ống',
                measureUnitId: 15,
                isDefault: true,
                measureRateName: 'Chuyển đổi Ống sang 1xỐng',
                level: 3,
                id: 63739,
                ratio: 1,
                uniqueId: 15,
                price: 334000,
              },
              {
                isSellDefault: false,
                measureRateId: 884,
                measureUnitName: 'Hộp',
                measureUnitId: 1,
                isDefault: false,
                measureRateName: 'Chuyển đổi Hộp sang 1 Ống',
                level: 1,
                id: 63758,
                ratio: 1,
                uniqueId: 1,
                price: 334000,
              },
            ],
            taxonomies: [
              {
                id: 106802,
                taxonomyId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
                taxonomyName: 'CÚM',
                level: 3,
              },
              {
                id: 110359,
                taxonomyId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
                taxonomyName: 'CÚM',
                level: 3,
              },
            ],
            requireBeforeInjection: 0,
            isMultiDose: false,
          },
          diseaseGroupId: '9b513cb2-016d-426b-a5fb-786166da7751',
          diseaseGroup: {
            name: 'CÚM',
            normalizeName: 'cum',
            disease: 'CÚM',
            engName: 'Influenza ',
            viName: 'Cúm',
          },
          isPregnantRegimen: false,
          injectionRoutes: [
            {
              vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
              usage: 'Tiêm bắp',
              isDefault: true,
              position: '',
              regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
              id: 'bb6850b4-f411-44d8-b813-155f01135e78',
              createdDate: '2023-08-16T11:20:06.3286250',
            },
          ],
        },
      },
      {
        date: '2024-12-01T12:00:00+07:00',
        regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
        sku: '00038149',
        status: 2,
        diseaseGroupId: '9b513cb2-016d-426b-a5fb-786166da7751',
        orderInjections: 2,
        regimen: {
          id: '2a492040-653a-403f-96c1-26efeac3efe9',
          priority: 3,
          matchingScore: 1,
          unitCode: 0,
          regimenType: 1,
          scheduleType: 'Từ 9 tuổi',
          vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
          ageUnit: 'Age',
          fromAge: '9 tuổi',
          toAge: '',
          ageUnitCode: 3,
          from: 9,
          to: 1000,
          ageFromUnitCode: 3,
          ageFromValue: 9,
          ageToUnitCode: 3,
          ageToValue: 1000,
          equalToAge: true,
          equalFromAge: true,
          requiredInjections: 1,
          details: [
            {
              regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
              order: 1,
              nearestInjectionDistance: '0',
              nearestInjectionDistanceValue: 0,
              distance: 0,
              isRequired: true,
              dosage: 0.5,
              unit: 'ml',
              minDistance: 0,
              maxDistance: 0,
              distanceValueByType: 0,
              distanceType: 1,
              dayAllowEarly: 60,
              regimenRules: [
                {
                  id: '926923c4-3b20-47e8-a7ba-947d926a4435',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'd44cf0f7-1144-40fc-bf28-32173db0586e',
                  sku: '00038149',
                  from: 1,
                  fromUnit: 0,
                  to: 0,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 2,
                  alertLevel: 2,
                  createdDate: '2024-12-19T16:53:21.826344',
                  createdBy: 'system',
                  modifiedDate: '2024-12-19T16:53:21.826344',
                  modifiedBy: 'system',
                  isActive: true,
                  isDeleted: false,
                },
              ],
              ageFromUnitCode: 3,
              ageFromValue: 9,
              ageToUnitCode: 3,
              ageToValue: 1000,
              equalFromAge: true,
              equalToAge: true,
              isActive: true,
            },
            {
              regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
              order: 2,
              nearestInjectionDistance: '30 x 12',
              nearestInjectionDistanceValue: 360,
              distance: 360,
              isRequired: false,
              repeatFrequency: '01 NĂM',
              dosage: 0.5,
              unit: 'ml',
              minDistance: 358,
              maxDistance: 360,
              distanceValueByType: 12,
              distanceType: 1,
              dayAllowEarly: 60,
              regimenRules: [
                {
                  id: '04ea4c77-6816-43e9-89f7-87e9963f450a',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                  sku: '00038149',
                  from: 1,
                  fromUnit: 0,
                  to: 0,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 2,
                  alertLevel: 2,
                  createdDate: '2024-12-19T16:53:21.826344',
                  createdBy: 'system',
                  modifiedDate: '2024-12-19T16:53:21.826344',
                  modifiedBy: 'system',
                  isActive: true,
                  isDeleted: false,
                },
                {
                  id: '4c335a96-d428-4835-98f5-fb12046496b2',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                  sku: '00038149',
                  from: 30,
                  fromUnit: 0,
                  to: 240,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 1,
                  alertLevel: 1,
                  createdDate: '2024-08-15T13:54:08.658296',
                  createdBy: 'system',
                  modifiedDate: null,
                  modifiedBy: '',
                  isActive: true,
                  isDeleted: false,
                },
                {
                  id: '1d8d97f3-75c0-4c90-bd30-c45c4d0e73f3',
                  regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
                  regimenDetailId: 'fc0eb4d9-3d47-4f42-93a1-e3903f2d0edf',
                  sku: '00038149',
                  from: 241,
                  fromUnit: 0,
                  to: 0,
                  toUnit: 0,
                  equalFrom: true,
                  equalTo: true,
                  regimenRulesType: 1,
                  alertLevel: 2,
                  createdDate: '2024-08-15T13:54:08.658296',
                  createdBy: 'system',
                  modifiedDate: null,
                  modifiedBy: '',
                  isActive: true,
                  isDeleted: false,
                },
              ],
              ageFromUnitCode: 3,
              ageFromValue: 9,
              ageToUnitCode: 3,
              ageToValue: 1000,
              equalFromAge: true,
              equalToAge: true,
              isActive: true,
            },
          ],
          isDefault: true,
          baseScore: 0,
          note: 'Mũi 1: Lần tiêm đầu tiên\nMũi nhắc: 01 mũi nhắc hàng năm.',
          comboType: 0,
          vaccine: {
            id: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
            sku: '00038149',
            name: 'INFLUVAC TETRA',
            type: 'Bất hoạt',
            typeCode: 2,
            pregnantNoteCode: 0,
            genderName: 'Tất cả',
            gender: -1,
            pregnantNote: 'Không có',
            note: '',
            preinjectionTest: false,
            minAge: 'Từ 6 tháng',
            maxAge: '',
            manufactor: 'Hà Lan',
            producer: '',
            brand: 'Abbott',
            brandOrigin: 'Hoa Kỳ',
            vaccineDiseases: [
              {
                vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
                diseaseId: '0e4d6d13-1b97-4f6d-ac44-d3e05ce17153',
                diseaseName: 'CÚM',
                id: '13d8e855-4613-4f54-bf21-c91a0eba0898',
                createdDate: '2023-12-02T17:41:22.9096370',
              },
            ],
            injectionRoutes: [],
            indicationNotes: [
              {
                contraindications:
                  'Các lưu ý khác: \n- CCĐ: Quá mẫn với thành phần trứng, formaldehyde, cetyltrimethylamoni bromid, polysorbat 80, gentamicin (thành phần của vắc xin Influvac Tetra)',
                precautions: '',
                otherNote: '',
                postponement: '',
                transfer2Hospital: '',
              },
            ],
            vaccineInteractions: [
              {
                vaccineId: 'e1206ca5-58ba-4e04-b27a-47a80db573ef',
                sku: '00045856',
                name: 'QDENGA',
                minDistance: '1 ngày',
                minDistanceValue: 1,
                note: 'Vaccine QDENGA chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                warningType: 0,
                warningContent:
                  'Vaccine QDENGA chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                isInjectionSameDay: false,
                interactionType: 4,
              },
              {
                vaccineId: 'b046a06e-cfc1-4327-9bfe-8efd07372dbd',
                sku: '00043237',
                name: 'BEXSERO',
                minDistance: '1 ngày',
                minDistanceValue: 1,
                note: 'Vaccine BEXSERO chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                warningType: 0,
                warningContent:
                  'Vaccine BEXSERO chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với vaccine INFLUVAC TETRA',
                isInjectionSameDay: false,
                interactionType: 4,
              },
              {
                vaccineId: '9fc2c76a-531a-458f-9703-3fd9c5f8a15f',
                sku: '00038255',
                name: 'VAXIGRIP TETRA',
                minDistance: '28 ngày',
                minDistanceValue: 28,
                note: 'Vắc xin có cùng thành phần nên tiêm cách nhau 28 ngày',
                warningType: 0,
                warningContent:
                  'Tương tác vắc xin cùng thành phần: INFLUVAC TETRA và VAXIGRIP TETRA nên tiêm cách nhau 28 ngày',
                isInjectionSameDay: false,
                interactionType: 1,
              },
              {
                vaccineId: 'c85f8ba6-bb27-4deb-a834-1c4466d83113',
                sku: '00038218',
                name: 'GCFLU QUADRIVALENT',
                minDistance: '28 ngày',
                minDistanceValue: 28,
                note: 'Vắc xin có cùng thành phần nên tiêm cách nhau 28 ngày',
                warningType: 0,
                warningContent:
                  'Tương tác vắc xin cùng thành phần: INFLUVAC TETRA và GCFLU QUADRIVALENT nên tiêm cách nhau 28 ngày',
                isInjectionSameDay: false,
                interactionType: 1,
              },
            ],
            vaccineReplacements: [],
            prices: [
              {
                id: 185072,
                sku: '00038149',
                priceBookId: 1,
                productMeasureUnitId: 63758,
                level: 1,
                price: 334000,
                measureUnitId: 1,
                measureUnitName: 'Hộp',
                fromDate: '2024-11-26T00:00:00',
                toDate: '2050-12-31T00:00:00',
                approvedDate: '2024-11-26T08:25:21',
              },
              {
                id: 185073,
                sku: '00038149',
                priceBookId: 1,
                productMeasureUnitId: 63739,
                level: 3,
                price: 334000,
                measureUnitId: 15,
                measureUnitName: 'Ống',
                fromDate: '2024-11-26T00:00:00',
                toDate: '2050-12-31T00:00:00',
                approvedDate: '2024-11-26T08:25:21',
              },
            ],
            categories: [
              {
                level: 1,
                id: 1734702,
                categoryName: 'VACCINE',
                categoryId: 'ac3a2499-64e9-46f2-acfa-3a0ca9153694',
                uniqueId: 34,
              },
              {
                level: 2,
                id: 1734703,
                categoryName: 'CÚM',
                categoryId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
                uniqueId: 3881,
              },
            ],
            measures: [
              {
                isSellDefault: true,
                measureRateId: 28,
                measureUnitName: 'Ống',
                measureUnitId: 15,
                isDefault: true,
                measureRateName: 'Chuyển đổi Ống sang 1xỐng',
                level: 3,
                id: 63739,
                ratio: 1,
                uniqueId: 15,
                price: 334000,
              },
              {
                isSellDefault: false,
                measureRateId: 884,
                measureUnitName: 'Hộp',
                measureUnitId: 1,
                isDefault: false,
                measureRateName: 'Chuyển đổi Hộp sang 1 Ống',
                level: 1,
                id: 63758,
                ratio: 1,
                uniqueId: 1,
                price: 334000,
              },
            ],
            taxonomies: [
              {
                id: 106802,
                taxonomyId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
                taxonomyName: 'CÚM',
                level: 3,
              },
              {
                id: 110359,
                taxonomyId: 'f27b72c5-9625-4a1a-bea0-5443e4de7f1b',
                taxonomyName: 'CÚM',
                level: 3,
              },
            ],
            requireBeforeInjection: 0,
            isMultiDose: false,
          },
          diseaseGroupId: '9b513cb2-016d-426b-a5fb-786166da7751',
          diseaseGroup: {
            name: 'CÚM',
            normalizeName: 'cum',
            disease: 'CÚM',
            engName: 'Influenza ',
            viName: 'Cúm',
          },
          isPregnantRegimen: false,
          injectionRoutes: [
            {
              vaccineId: 'f5d7aebf-4c9a-4761-91ef-60cbdcd35638',
              usage: 'Tiêm bắp',
              isDefault: true,
              position: '',
              regimenId: '2a492040-653a-403f-96c1-26efeac3efe9',
              id: 'bb6850b4-f411-44d8-b813-155f01135e78',
              createdDate: '2023-08-16T11:20:06.3286250',
            },
          ],
        },
      },
    ];

    it('Đụng ru tới sớm', () => {
      scheduleRulesService.checkRuleDistance(date, current, arrFuture, rules);
      const ruleDistance = rules?.find(
        (rule) => rule?.ruleName === 'Khách hàng đến sớm so với khoảng cách tiêu chuẩn của phác đồ',
      );
      expect(ruleDistance).toBeDefined();
    });

    it('Pass ru tới sớm', () => {
      const dateValid = new Date('2025-11-02T00:00:00Z');
      current.date = new Date('2025-11-02T00:00:00Z');
      const earlyRules: RuleRes[] = [];
      scheduleRulesService.checkRuleDistance(dateValid, current, arrFuture, earlyRules);
      const ruleDistance = earlyRules?.find(
        (rule) => rule?.ruleName === 'Khách hàng đến sớm so với khoảng cách tiêu chuẩn của phác đồ',
      );
      expect(ruleDistance).toBeUndefined();
    });
  });
});
