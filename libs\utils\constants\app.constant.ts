export const API_PREFIX = 'api';
export const API_TIMEOUT = 30000;
export const DEFAULT_PAGE_SIZE = 10;
export const GUID_EMPTY = '00000000-0000-0000-0000-000000000000';
export const APPLICATION_ID = 'd96d1cc1-31f8-4594-bf1e-0c436538adbb';
export const TENANT_ID_ICT = 'e6770008-4aea-4ee6-aede-691fd22f5c14';

export const LOG_LEVEL = {
  INFO: 'info',
  ERROR: 'error',
  WARN: 'warn',
  DEBUG: 'debug',
  VERBOSE: 'verbose',
};

export const DEFAULT_DATE_ADD = 30;
export const DEFAULT_DATE_ADD_WITH_NOT_STOCK = 7;
export const TIMEZONE = '+07:00';
export const FORMAT_DATE = 'YYYY-MM-DD';
export const RSA_ECOM_HARD_DEFAULT_SHOP_CODE = '50001';
export const DEFAULT_DATE_SKU_LIMIT = +process.env.DEFAULT_DATE_SKU_LIMIT || 10;
export const RATE_GOOD_ATTRIBUTE_ID = 9671;

export enum EnumDistanceType {
  days = 0,
  months = 1,
  years = 2,
}
export const EnumDistanceTypeString: Record<number, string> = {
  0: 'ngày',
  1: 'tháng',
  2: 'năm',
};

export enum RuleType {
  DenSom = 0,
  ChuaDuTuoi = 1,
  QuaTuoi = 2,
  HaiTiemMotUong = 3,
  TuongTac = 4,
  HaiMuiCungNhomBenh = 5,
  HetTon = 6,
  QuaMuiToiDa = 8,
  HetTonHangKhangHiem = 9,
  ChuyenDoiVacXin = 10,
  GioiHanPhacDo = 11,
}
