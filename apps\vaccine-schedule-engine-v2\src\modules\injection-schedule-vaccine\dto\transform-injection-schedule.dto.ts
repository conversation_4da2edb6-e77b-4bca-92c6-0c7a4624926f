import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

/*
  appointmentDate => ngày tiêm
  injections => mũi tiêm
  manufactor => nhà sản xuất
  taxonomies => loại vaccine
  diseaseName => tên vaccine
*/
export class TransformInjectionSchedule {
  @ApiProperty()
  @Expose()
  waittingPaid?: string;

  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty({ type: Date })
  @Expose()
  appointmentDate: string | Date;

  @ApiProperty()
  @Expose()
  injections: number;

  @ApiProperty()
  @Expose()
  manufactor: string;

  @ApiProperty()
  @Expose()
  taxonomies: string;

  @ApiProperty()
  @Expose()
  vaccineName: string;

  @ApiProperty()
  @Expose()
  sku: string;

  @ApiProperty()
  @Expose()
  shopName: string;

  @ApiProperty()
  @Expose()
  shopCode: string;

  @ApiProperty()
  @Expose()
  statusPayment: number;

  @ApiProperty()
  @Expose()
  statusAppointment: number;

  @ApiProperty()
  @Expose()
  customerNote: string;

  @ApiProperty()
  @Expose()
  createdByName: string;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty({ type: Boolean, example: false })
  @Expose()
  isScheduleFromHistory: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  personId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isPaid?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orderDetailAttachmentId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orderCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orderDetailAttachmentCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  sourceId?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  regimenId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  locationName?: string;
  lcvId?: string;

  @ApiProperty()
  @Expose()
  unitCodeSale?: string;

  @ApiProperty()
  @Expose()
  unitNameSale?: string;
}
