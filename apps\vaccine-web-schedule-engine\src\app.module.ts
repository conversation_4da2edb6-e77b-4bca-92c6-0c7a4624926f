import { Module, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { WinstonModule } from 'nest-winston';
import { BadRequestExceptionFilter, HttpExceptionFilter } from '@app/utils/common/filters';
import { AllExceptionsFilter } from '@app/utils/common/filters/all-exception.filter';
import { AuthGuard } from '@app/utils/common/guards';
import { LoggingInterceptor, TimeoutInterceptor, TransformInterceptor } from '@app/utils/common/interceptors';
import { envValidator } from '@app/utils/common/validations';
import { WinstonLoggerConfigService } from './config';
import { HealthModule } from './modules/health/health.module';
import { SchedulesWebModule } from './modules/schedules/schedules.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: `${process.cwd()}/apps/vaccine-web-schedule-engine/.env`,
      isGlobal: true,
      validate: envValidator,
    }),
    WinstonModule.forRootAsync({
      useClass: WinstonLoggerConfigService,
    }),
    // MongooseModule.forRootAsync({
    //   useClass: MongooseConfigService,
    // }),
    HealthModule,
    SchedulesWebModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        whitelist: false,
        skipMissingProperties: false,
        transform: true,
      }),
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: BadRequestExceptionFilter,
    },
  ],
})
export class AppModule {}
