import { Module } from '@nestjs/common';
import { InjectionScheduleVaccineController } from './controllers/injection-schedule-vaccine.controller';
import { InjectionScheduleVaccineService } from './services/injection-schedule-vaccine.service';
import { VacHistoryModule } from 'vac-nest-history';
import { ScheduleCoreModule } from 'vac-nest-schedule';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { InjectionScheduleUltil } from './services/injectionSchedule.ultil';
import { PIMAppModule } from 'vac-nest-pim-app';

@Module({
  imports: [VacHistoryModule, ScheduleCoreModule, ExaminationCoreModule, PIMAppModule],
  controllers: [InjectionScheduleVaccineController],
  providers: [InjectionScheduleVaccineService, InjectionScheduleUltil],
})
export class InjectionScheduleVaccineModule {}
