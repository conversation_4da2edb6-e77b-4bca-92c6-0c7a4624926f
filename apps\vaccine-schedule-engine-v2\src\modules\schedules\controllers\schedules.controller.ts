import { Body, Controller, HttpCode, HttpStatus, Inject, Post } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Public } from 'apps/vaccine-schedule-engine-v2/src/common/decorators';
import { CustomHeaders, generalSchema } from 'apps/vaccine-schedule-engine-v2/src/utilities/function-swagger';
import { CDSEngineService } from 'vac-nest-cds-engine';
import { ClassResponse } from '../../../utilities/class-response';
import { CheckAllRuleSchedulesDto, CheckAllRuleSchedulesRes } from '../dto/check-all-rule-schedules.dto';
import { SuggestScheduleDto } from '../dto/suggest-schedule.dto';
import { SchedulesService } from '../services/schedules.service';
import { MongooseService } from '../services/mongoose.service';
import { TableName } from '../constants';
import moment from 'moment';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Controller({ path: 'schedules', version: '1' })
@ApiTags('Schedules')
@CustomHeaders()
@ApiExtraModels(ClassResponse, CheckAllRuleSchedulesRes)
export class SchedulesController {
  constructor(
    private readonly schedulesService: SchedulesService,
    private readonly cDSEngineService: CDSEngineService,
    private readonly mongooseService: MongooseService,

    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  @Post('suggest')
  @Public()
  @HttpCode(HttpStatus.OK)
  async suggest(@Body() suggestScheduleDto: SuggestScheduleDto) {
    const res = await this.schedulesService.suggestSchedule(suggestScheduleDto);
    const uuid = uuidv4();
    const headers = { ...this.req.headers, 'X-Request-ID': uuid } as any;
    // this.cDSEngineService.suggest({ ...suggestScheduleDto, requestId: uuid }, { headers });
    this.mongooseService.handleAddData(TableName.SUGGEST, [
      {
        headers,
        screenType: suggestScheduleDto?.screenType,
        lcvId: suggestScheduleDto?.arrCurrent?.at(0)?.lcvId,
        ticketCode: suggestScheduleDto?.ticketCode,
        expectedDate: suggestScheduleDto?.expectedDate,
        payload: suggestScheduleDto,
        response: res,
        createDate: moment().utcOffset(7).format(),
        'X-Request-ID': uuid,
      },
    ]);
    return res;
  }

  @Post('validate')
  @Public()
  @HttpCode(HttpStatus.OK)
  async validate(@Body() suggestScheduleDto: SuggestScheduleDto) {
    const res = await this.schedulesService.validateSchedule(suggestScheduleDto);
    const uuid = uuidv4();
    const headers = { ...this.req.headers, 'X-Request-ID': uuid } as any;
    // this.cDSEngineService.validate({ ...suggestScheduleDto, requestId: uuid }, { headers });
    this.mongooseService.handleAddData(TableName.VALIDATE, [
      {
        headers,
        screenType: suggestScheduleDto?.screenType,
        lcvId: suggestScheduleDto?.arrCurrent?.at(0)?.lcvId,
        ticketCode: suggestScheduleDto?.ticketCode,
        expectedDate: suggestScheduleDto?.expectedDate,
        payload: suggestScheduleDto,
        response: res,
        createDate: moment().utcOffset(7).format(),
        'X-Request-ID': uuid,
      },
    ]);
    return res;
  }

  @Post('check')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch hẹn',
    schema: generalSchema(CheckAllRuleSchedulesRes, 'object'),
  })
  async checkSchedules(@Body() checkAllRuleSchedulesDto: CheckAllRuleSchedulesDto) {
    const res = await this.schedulesService.checkAllSchedules(checkAllRuleSchedulesDto);
    const uuid = uuidv4();
    const headers = { ...this.req.headers, 'X-Request-ID': uuid } as any;
    // this.cDSEngineService.check({ ...checkAllRuleSchedulesDto, requestId: uuid }, { headers });
    this.mongooseService.handleAddData(TableName.CHECK, [
      {
        headers,
        screenType: checkAllRuleSchedulesDto?.screenType,
        lcvId: checkAllRuleSchedulesDto?.arrCurrent?.at(0)?.lcvId,
        ticketCode: checkAllRuleSchedulesDto?.ticketCode,
        expectedDate: checkAllRuleSchedulesDto?.expectedDate,
        payload: checkAllRuleSchedulesDto,
        response: res,
        createDate: moment().utcOffset(7).format(),
        'X-Request-ID': uuid,
      },
    ]);
    return res;
  }

  @Post('suggest-for-pricing')
  @Public()
  @HttpCode(HttpStatus.OK)
  async suggestForPricing(@Body() suggestScheduleDto: SuggestScheduleDto) {
    return this.schedulesService.suggestScheduleForPricing(suggestScheduleDto);
  }
}
