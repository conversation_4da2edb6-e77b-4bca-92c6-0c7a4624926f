# Rule Avoid Duplicate Immunogenic

## M<PERSON> tả

Rule `ruleAvoidDuplicateImmunogenic` được tạo để tránh trùng lặp miễn dịch giữa các vaccine. Rule này sẽ kiểm tra trong `current.regimen?.vaccine.vaccineInteractions` để tìm các interaction có `interactionType = 5`, sau đó kiểm tra xem SKU của interaction đó có tồn tại trong danh sách `arrFutureFilter` hay không.

## Vị trí triển khai

Rule này được thêm vào **chỉ trong vaccine-schedule-engine-v2** (không có trong web-schedule-engine) ở những vị trí sau:

### 1. Trong `schedules.service.ts` - method `suggestSchedule`

```typescript
this.scheduleRulesService.ruleAvoidDuplicateImmunogenic(current, arrFuture, rules);
```

### 2. Trong `schedules.service.ts` - method `checkAllSchedules`

#### Case ECOM_PAYCHECK:

```typescript
this.scheduleSwitchRulesService.checkScheduleSwitchRule(...);
this.scheduleRulesService.ruleAvoidDuplicateImmunogenic(current, arrFutureFiler, rules);
this.scheduleRulesService.checkRuleInventory(...);
```

#### Case RSA_PAYCHECK:

```typescript
this.scheduleSwitchRulesService.checkScheduleSwitchRule(...);
this.scheduleRulesService.ruleAvoidDuplicateImmunogenic(current, arrFutureFiler, rules);
```

#### Case default:

```typescript
this.scheduleRulesService.checkRuleInventory(...);
this.vaccineConversionRuleService.checkVaccineConversionRule(...);
this.scheduleRulesService.checkRuleConflict(...);
this.scheduleRulesService.checkRuleDistance(...);
this.scheduleRulesService.checkRuleAge(...);
this.ruleMaxInjectionService.checkRuleMaxInjectionDiseaseGroup(...);
this.scheduleSwitchRulesService.checkScheduleSwitchRule(...);
this.scheduleRulesService.ruleAvoidDuplicateImmunogenic(current, arrFutureFiler, rules);
```

## Logic hoạt động

1. **Tìm interactions có type = 5**: Rule sẽ filter trong `current.regimen?.vaccine?.vaccineInteractions` để tìm các interaction có `interactionType === 5`

2. **Kiểm tra SKU trong future list**: Với mỗi interaction tìm được, rule sẽ tìm xem `interaction.sku` có tồn tại trong `arrFutureFilter` hay không

3. **Thêm rule nếu tìm thấy**: Nếu tìm thấy SKU trong future list, rule sẽ được thêm vào mảng `rules` với:

   - `text`: "Tôi sẽ định nghĩa sau" (placeholder)
   - `type`: Được xác định bởi `ruleTypeDistance()` dựa trên `screenType` và `warningType`
   - `value`: `interaction.minDistanceValue` hoặc 0
   - `ruleType`: `RuleType.TuongTac` (4) - Sử dụng RuleType hiện có thay vì tạo mới
   - `ruleName`: "Tránh trùng lặp miễn dịch [vaccine hiện tại] với [vaccine tương lai]"
   - `isAllowEvaluate`: true

4. **Return**: Trả về `true` nếu tìm thấy và thêm rule, `false` nếu không

## Ví dụ sử dụng

### Dữ liệu đầu vào:

```typescript
const current = {
  sku: 'SKU001',
  regimen: {
    vaccine: {
      name: 'Vaccine A',
      vaccineInteractions: [
        {
          sku: 'SKU002',
          interactionType: 5, // Type cần tìm
          warningType: 0,
          minDistanceValue: 28,
        },
      ],
    },
  },
  screenType: 'INDICATION',
};

const arrFutureFilter = [
  {
    sku: 'SKU002', // SKU trùng với interaction
    regimen: {
      vaccine: {
        name: 'Vaccine B',
      },
    },
  },
];
```

### Kết quả:

```typescript
// Rule sẽ được thêm vào mảng rules:
{
  text: 'Tôi sẽ định nghĩa sau',
  type: 'DANGER',
  value: 28,
  ruleType: 4, // RuleType.TuongTac
  ruleName: 'Tránh trùng lặp miễn dịch Vaccine A với Vaccine B',
  isAllowEvaluate: true
}
```

## Tích hợp với checkRuleDistance

Để tránh xung đột logic, rule `checkRuleDistance` đã được cập nhật để **loại trừ** `interactionType === 5`:

```typescript
// Filter vaccine interactions loại trừ interactionType === 5
const filteredVaccineInteractions = current?.regimen?.vaccine?.vaccineInteractions?.filter(
  (interaction) => interaction.interactionType !== 5,
);

// Sử dụng filtered list thay vì toàn bộ interactions
const interactionFind = filteredVaccineInteractions?.find((e) => e.sku === past.sku);
```

Điều này đảm bảo:

- `checkRuleDistance` chỉ xử lý các interaction type khác (1, 2, 3, 4, etc.)
- `ruleAvoidDuplicateImmunogenic` chuyên xử lý `interactionType === 5`
- Không có xung đột logic giữa hai rules

## Test Coverage

File test `ruleAvoidDuplicateImmunogenic.spec.ts` bao gồm các test cases:

- Không có interactions
- Không có interactions với type 5
- SKU không tồn tại trong future list
- SKU tồn tại trong future list (case chính)
- Multiple interactions với type 5
- Xử lý screenType SLIDEBAR
- Xử lý missing vaccine name

## API Endpoint

Rule này chỉ hoạt động trong API "check" (`POST /schedules/check`) như yêu cầu ban đầu.

## Cập nhật gần đây

1. **Loại bỏ RuleType mới**: Sử dụng `RuleType.TuongTac` hiện có
2. **Tích hợp với checkRuleDistance**: Thêm logic filter để tránh xung đột
3. **Tối ưu performance**: Sử dụng biến `filteredVaccineInteractions` để tránh filter nhiều lần
4. **Chỉ triển khai trong vaccine-schedule-engine-v2**: Không triển khai trong web-schedule-engine
