import { Injectable } from '@nestjs/common';
import { InjectS3, S3Client } from 'ict-nest-s3';
import { S3_OPERATIONS } from 'ict-nest-s3/dist/s3.constant';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import generateUploadFolderPath from '@app/utils/utilities/generate-upload-folder-path';
import { GetS3UploadLinkDto, GetS3UploadLinkResponse } from '../dto/get-s3-upload-link.dto';
import { GetS3PresignDto } from '../dto/get-s3-presign.dto';
const PRE_SIGNED_LINK_EXPIRATION = 3600;

@Injectable()
export class FilesService {
  constructor(
    @InjectS3(process.env.S3_RSA_CONNECTION)
    private readonly s3Client: S3Client,
  ) {}

  async getS3UploadLinks(getS3UploadLinkDto: GetS3UploadLinkDto): Promise<GetS3UploadLinkResponse> {
    const { others } = getS3UploadLinkDto;
    const uuid = uuidv4();
    const folderPath = generateUploadFolderPath();
    const keys = [];

    for (let i = 0; i < others; i++) {
      keys.push(path.join(folderPath, `others_${i}_${uuid}`));
    }

    return { links: await this.s3Client.presignObjects(keys, S3_OPERATIONS.PUT_OBJECT, PRE_SIGNED_LINK_EXPIRATION) };
  }

  async getS3Presign(getS3UploadLinkDto: GetS3PresignDto): Promise<GetS3UploadLinkResponse> {
    const { urls } = getS3UploadLinkDto;
    return { links: await this.s3Client.presignObjects(urls, S3_OPERATIONS.GET_OBJECT, PRE_SIGNED_LINK_EXPIRATION) };
  }
}
