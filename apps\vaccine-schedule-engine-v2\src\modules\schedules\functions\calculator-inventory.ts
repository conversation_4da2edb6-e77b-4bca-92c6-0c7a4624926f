import { StockMedicRes } from 'vac-nest-ims';
import { CountScheduleRes, CountShopScheduleRes, WHS_CODE_NORMAL } from 'vac-nest-schedule';

export const handleCalculatorInventory = (
  currentSku: string,
  stockRes: StockMedicRes[],
  stock: StockMedicRes,
  scheduleFind: CountShopScheduleRes,
  countScheduleFind: CountScheduleRes,
) => {
  // Nếu có lịch hẹn tiêm cho người khác thì tính lại tồn (để tồn ưu tiền người mua trước)
  if (countScheduleFind && scheduleFind) {
    // 1.N<PERSON>u tồn > lịch hẹn 1 đơn vị => tính lại tồn - lịch hẹn
    // 2.Nếu tồn = lịch hẹn + 1 => tính đơn vị dưới level hiện tại để check còn đủ tồn không
    // 3.<PERSON><PERSON><PERSON> tồn <= lịch hẹn => hết tồn
    if (stock?.quantityAvailable > countScheduleFind?.count + 1) {
      //1.<PERSON><PERSON><PERSON> tồn > lịch hẹn 1 đơn vị
      stock.quantityAvailable = stock?.quantityAvailable - countScheduleFind?.count;
    } else if (stock.quantityAvailable === countScheduleFind?.count + 1) {
      // 2.Nếu tồn = lịch hẹn + 1
      // Lấy tất cả level thấp hơn level hiện tại sort giảm dần
      const countScheduleFindLowerLevel: CountScheduleRes = scheduleFind?.countSchedule?.find(
        (e) => e?.level === countScheduleFind?.level + 1,
      );
      const stockLowerCurrentLevel: StockMedicRes = stockRes?.find(
        (s) =>
          s.sku === currentSku &&
          s.unitLevel === stock?.unitLevel + 1 &&
          String(s.whsCode).slice(-3) === WHS_CODE_NORMAL,
      );

      /**
       * Lấy level thấp nhất của kho, schedule so sánh
       * Nếu không đủ 1sl của level hiện tại thì phải trừ 1 level hiện tại
       *   (có hẹn 1 liều mà tồn đúng 1lọ => phải trừ 1 lọ)
       * Nếu vẫn còn số lượng quy đổi thành level hiện tại được thì vẫn tính tồn bt
       *   (có hẹn 1 liều mà tồn đúng 1lọ và 2 liều => vẫn mua lọ được)
       */
      if (
        countScheduleFindLowerLevel &&
        stockLowerCurrentLevel &&
        stockLowerCurrentLevel?.quantityAvailable -
          stock?.quantityAvailable * countScheduleFind?.ratio -
          countScheduleFindLowerLevel?.count <
          countScheduleFindLowerLevel?.ratio
      ) {
        stock.quantityAvailable = stock.quantityAvailable - countScheduleFind?.count - 1;
      } else {
        stock.quantityAvailable = stock.quantityAvailable - countScheduleFind?.count;
      }
    } else {
      // 3.Nếu tồn <= lịch hẹn
      stock.quantityAvailable = 0;
    }
  }
};
