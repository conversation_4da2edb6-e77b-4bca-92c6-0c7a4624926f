export class ErrorCode {
  static RSA_OSR_DATA_NOT_FOUND = 'RSA_OSR_DATA_NOT_FOUND:000400';
  static RSA_CANCEL_ORDER_FORBIDDEN = 'RSA_RSA_CANCEL_ORDER_FORBIDDEN:000403';
  static RSA_CHANGE_ORDER_TYPE_FAILED = 'RSA_CHANGE_ORDER_TYPE_FAILED:000403';
  static RSA_VOUCHER_OF_PRE_ORDER = 'RSA_VOUCHER_OF_PRE_ORDER:000403';
  static RSA_UPDATE_REGISTERED_FAILED = 'RSA_UPDATE_REGISTERED_FAILED:000400';
  static RSA_UNVERIFIED_VOUCHER_OTP = 'RSA_UNVERIFIED_VOUCHER_OTP:000403';
  static RSA_VOUCHER_PARTNER = 'RSA_VOUCHER_PARTNER:000403';
  static RSA_CONTRACT_NOT_FOUND = 'RSA_CONTRACT_NOT_FOUND:000400';
  static RSA_USER_SM = 'RSA_USER_SM:000403';
  static HEADER_VALIDATE = 'HEADER_VALIDATE:000400';
  static INTERNAL_ROTATION_EXISTED = 'INTERNAL_ROTATION_EXISTED:000403';
  static INTERNAL_ROTATION_ENOUGH_QUANTITY = 'INTERNAL_ROTATION_ENOUGH_QUANTITY:000403';
  static ORDER_RULE_DISCOUNT_ADJUSTMENT = 'ORDER_RULE_DISCOUNT_ADJUSTMENT:000403';
  static OCR_CAN_NOT_DETECT = 'OCR_CAN_NOT_DETECT';
  static RSA_VERIFY_USER = 'RSA_VERIFY_USER:000413';
  static RSA_CASE_REFUND_PAYMENT_DEPOSIT_NOT_FOUND = 'RSA_CASE_REFUND_PAYMENT_DEPOSIT_NOT_FOUND:001403';
  static RSA_CASE_REFUND_PAYMENT_DEPOSIT_AMOUNT_ZERO = 'RSA_CASE_REFUND_PAYMENT_DEPOSIT_AMOUNT_ZERO:004403';
  static RSA_DEPOSIT_CANCEL_NOT_FOUND = 'RSA_REFUND_CONFIRM_NOT_FOUND:000400';
  static RSA_REFUND_CASH_TYPE_NOT_FOUND = 'RSA_REFUND_CASH:000400';
  static RSA_DEPOSIT_CANCEL_PHONE_NOT_FOUND = 'RSA_DEPOSIT_CANCEL:000400';
  static RSA_TYPE_CONFIRM_REFUND_CASH_NOT_FOUND = 'RSA_TYPE_CONFIRM_REFUND_CASH_NOT_FOUND:000400';
  static RSA_REFUND_REASON_CASH_NOT_FOUND = 'RSA_REFUND_REASON_CASH:000404';
  static RSA_DEPOSIT_CANCEL_PAYMENT_FAILED = 'RSA_DEPOSIT_CANCEL:000413';
  static RSA_DEPOSIT_CANCEL_FAILED = 'RSA_DEPOSIT_CANCEL:000403';
  static RSA_DETAIL_BARCODE = 'RSA_DETAIL_BARCODE:00403';
  static RSA_PRICING_NOT_FOUND = 'RSA_PRICING:00404';
  static RSA_UPLOAD_FAIL = 'RSA_UPLOAD:00500';
  static PRODUCT_EXIST = 'RSA_PRODUCT_EXIST:00409';
  static CART_EMPTY = 'RSA_CART_EMPTY:00403';
  static CHECK_IMEI_FORBIDDEN = 'RSA_CHECK_IMEI_ERROR:00403';
  static NOT_FOUND = 'RSA_ERROR:00404';
  static CONFLICT = 'RSA_ERROR:00409';
  static UNAUTHORIZED = 'RSA_ERROR:00401';
  static BAD_REQUEST = 'RSA_ERROR:00400';
  static INTERNAL_SERVER = 'RSA_ERROR:00500';
  static CORE_INTERNAL_SERVER = 'CORE_ERROR:00500';
  static INVALID_HOME_CREDIT_CONTRACT_SCHEME = 'RSA_HOMECREDIT_CONTRACT_SCHEME:00170';
  static INVALID_HOME_CREDIT_CONTRACT_SCHEME_PROPERTY = 'INVALID_HOME_CREDIT_CONTRACT_SCHEME_PROPERTY:00170';
  static PREORDER_COMBINE_WARRANTY = 'RSA_PREORDER_COMBINE_WARRANTY:000403';
  static IP15_CANCEL_ORDER = 'RSA_IP15_CANCEL_ORDER:000403';
  static SAMSUNG_INSTALLMENT_IMEI = 'SAMSUNG_INSTALLMENT_IMEI:000403';
  static REGIMENT_RULE_TYPE_DISTANCE_WARING = 'REGIMENT_RULE_TYPE_DISTANCE_WARING:000403';
  static REGIMENT_RULE_TYPE_DISTANCE_DANGER = 'REGIMENT_RULE_TYPE_DISTANCE_DANGER:000403';
  static HCYK_OPEN = 'HCYK:000403';
  static BLOCK_GARDASIL_4_FOR_NAM = 'BLOCK_GARDASIL_4_FOR_NAM:000403';

  private static createErrorMap(): Map<string, string> {
    const errorCode = new Map();
    errorCode.set(this.IP15_CANCEL_ORDER, 'Iphone 15 series không thể huỷ cọc trong giai đoạn này!!!');
    errorCode.set(this.PREORDER_COMBINE_WARRANTY, 'Vui lòng thêm combo bảo hành 1 đổi 1 vào giỏ hàng!');
    errorCode.set(this.RSA_OSR_DATA_NOT_FOUND, 'Đơn hàng không có sản phẩm pre order hoặc đã hết thời gian pre order');
    errorCode.set(this.RSA_CANCEL_ORDER_FORBIDDEN, 'Không thể huỷ đơn hàng vì đơn hàng đã được thanh toán');
    errorCode.set(this.RSA_CHANGE_ORDER_TYPE_FAILED, 'Đơn hàng trả góp không được chuyển đổi loại đơn hàng khác');
    errorCode.set(this.RSA_VOUCHER_OF_PRE_ORDER, 'Voucher không được sử dụng ở giai đoạn này');
    errorCode.set(this.RSA_UPDATE_REGISTERED_FAILED, 'Không thể cập nhật thông tin đăng ký');
    errorCode.set(this.RSA_UNVERIFIED_VOUCHER_OTP, 'Voucher trong giỏ hàng chưa được verify!');
    errorCode.set(
      this.RSA_VOUCHER_PARTNER,
      'Voucher đối tác không thể thêm từ giỏ hàng. Vui lòng thêm từ màn hình payment!',
    );
    errorCode.set(this.RSA_CONTRACT_NOT_FOUND, 'Không tìm thấy thông tin hợp đồng trả góp');
    errorCode.set(this.RSA_USER_SM, 'User không có quyền thực hiện chức năng');
    errorCode.set(this.HEADER_VALIDATE, 'Header Phải có shopcode và order-channel');
    errorCode.set(this.INTERNAL_ROTATION_ENOUGH_QUANTITY, 'Không thể thực hiện LCNB vì đơn hàng đủ số lượng');
    errorCode.set(this.INTERNAL_ROTATION_EXISTED, 'Đơn hàng đã có LCNB trước đó');
    errorCode.set(this.ORDER_RULE_DISCOUNT_ADJUSTMENT, 'Không thể thực hiện điều chỉnh giá');
    errorCode.set(this.OCR_CAN_NOT_DETECT, 'Không thể lấy thông tin từ hình cung cấp');
    errorCode.set(this.RSA_VERIFY_USER, 'User sai otp');
    errorCode.set(this.RSA_CASE_REFUND_PAYMENT_DEPOSIT_NOT_FOUND, 'Không tìm thấy thông tin thanh toán cọc');
    errorCode.set(this.RSA_CASE_REFUND_PAYMENT_DEPOSIT_AMOUNT_ZERO, 'Lỗi chi tiền 0 đồng');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_NOT_FOUND, 'Không tìm thấy thông tin xác nhận hủy cọc');
    errorCode.set(this.RSA_REFUND_CASH_TYPE_NOT_FOUND, 'Không tìm thấy hình thức chi tiền');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_PHONE_NOT_FOUND, 'Không tìm số điện thoại mua hàng');
    errorCode.set(this.RSA_TYPE_CONFIRM_REFUND_CASH_NOT_FOUND, 'Không tìm thấy hình thức chi tiền');
    errorCode.set(this.RSA_REFUND_REASON_CASH_NOT_FOUND, 'Không tìm thấy lý do hủy cọc trong hệ thống');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_PAYMENT_FAILED, 'Không tìm thấy số tiền đặt cọc trong lịch sử thanh toán');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_FAILED, 'Không phải đơn hàng đặc cọc hoặc chưa hoàn tất cọc');
    errorCode.set(this.RSA_DETAIL_BARCODE, 'Sản phẩm không đủ imei');
    errorCode.set(this.RSA_PRICING_NOT_FOUND, 'Không tìm thấy sản phẩm trong pricing');
    errorCode.set(this.RSA_UPLOAD_FAIL, 'Lỗi không thể đẩy file lên hệ thống');
    errorCode.set(this.PRODUCT_EXIST, 'Thông tin đã có trong giỏ hàng.');
    errorCode.set(this.CART_EMPTY, 'Thông tin trên giỏ hàng rỗng.');
    errorCode.set(this.CHECK_IMEI_FORBIDDEN, 'Sản phẩm đã được đặt một nơi khác! vui lòng chọn imei khác');
    errorCode.set(this.NOT_FOUND, 'Không tìm thấy thông tin trong hệ thống');
    errorCode.set(this.CONFLICT, 'Thông tin đã có trong hệ thống');
    errorCode.set(this.UNAUTHORIZED, 'API cần phải có token của người dùng');
    errorCode.set(this.BAD_REQUEST, 'Đầu vào không hợp lệ');
    errorCode.set(this.INTERNAL_SERVER, 'Lỗi hệ thống');
    errorCode.set(this.CORE_INTERNAL_SERVER, 'Lỗi hệ thống tích hợp');
    errorCode.set(this.INVALID_HOME_CREDIT_CONTRACT_SCHEME, 'Gói trả góp có thể không tìm thấy');
    errorCode.set(this.INVALID_HOME_CREDIT_CONTRACT_SCHEME_PROPERTY, 'Gói trả góp sai thông tin');
    errorCode.set(
      this.SAMSUNG_INSTALLMENT_IMEI,
      'IMEI trong giỏ hàng khác với thông tin trong hợp đồng. Vui lòng chọn lại',
    );
    errorCode.set(
      this.REGIMENT_RULE_TYPE_DISTANCE_WARING,
      'Mũi {order} của {vaccineName} sớm hơn {earlyDate} ngày so với lịch tiêm theo phác đồ. Vui lòng kiểm tra lại trước khi xác nhận.',
    );
    errorCode.set(
      this.REGIMENT_RULE_TYPE_DISTANCE_DANGER,
      'Mũi {order} của {vaccineName} sớm hơn {earlyDate} ngày so với lịch tiêm theo phác đồ. Yêu cầu hội chẩn với trường hợp tiêm sớm từ {from} {unitFrom} trở lên so với lịch hẹn chuẩn.',
    );
    errorCode.set(
      this.HCYK_OPEN,
      'Khách hàng có phiếu Yêu cầu hội chẩn đang ở trạng thái Chờ hội chuẩn nên không thể thao tác tiếp. Vui lòng liên hệ Hội Đồng Y Khoa để được hội chẩn và tiếp tục.',
    );
    errorCode.set(this.BLOCK_GARDASIL_4_FOR_NAM, 'Không tiêm GARDASIL 4 0.5ML cho khách hàng là nam giới');
    return errorCode;
  }

  private static errorMap = ErrorCode.createErrorMap();

  static getError(code: string): string {
    if (this.errorMap.has(code)) {
      return this.errorMap.get(code);
    }
    return 'Error code has not been defined';
  }

  static defaultErrorCode() {
    return 'ERR:00000';
  }
}
