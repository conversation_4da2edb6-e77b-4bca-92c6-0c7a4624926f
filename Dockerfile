ARG NODE_IMAGE=node:16

FROM $NODE_IMAGE AS base

ARG APP_BUILD_ARG=vaccine-schedule-engine-v2
ENV APP_BUILD=$APP_BUILD_ARG

# Add User
RUN groupadd -g 10023 non-rootgroup && useradd -m -u 10023 -g non-rootgroup non-rootuser

# Create app directory
WORKDIR /usr/src/app

RUN chown -R non-rootuser:non-rootgroup /usr/src/app

RUN chown -R non-rootuser:non-rootgroup /usr/local

USER non-rootuser

RUN npm update -g npm

RUN npm install -g pm2

FROM base AS builder

COPY --chown=non-rootuser:non-rootgroup package*.json ./

COPY --chown=non-rootuser:non-rootgroup .npmrc ./

RUN npm ci

COPY --chown=non-rootuser:non-rootgroup . ./

RUN npm run build:$APP_BUILD
RUN npm ci --only=production --ignore-scripts && npm cache clean --force

FROM base AS runner
WORKDIR /usr/src/app

# Copy the bundled code from the build stage to the production image
COPY --chown=non-rootuser:non-rootgroup --from=builder /usr/src/app/node_modules ./node_modules
COPY --chown=non-rootuser:non-rootgroup --from=builder /usr/src/app/dist/apps/$APP_BUILD ./dist
COPY --chown=non-rootuser:non-rootgroup --from=builder /usr/src/app/package*.json ./
COPY --chown=non-rootuser:non-rootgroup --from=builder /usr/src/app/tsconfig.json ./
COPY --chown=non-rootuser:non-rootgroup --from=builder /usr/src/app/libs ./libs

# COPY ./apps/$APP_BUILD/.env.development ./.env
COPY ./apps/$APP_BUILD/pm2-config.yml ./

EXPOSE 3000

CMD ["pm2-runtime", "start", "pm2-config.yml"]