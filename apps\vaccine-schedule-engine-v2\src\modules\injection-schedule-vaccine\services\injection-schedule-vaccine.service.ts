import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import { GetHistoryScheduleByPersonsDto, VacHistoryService } from 'vac-nest-history';
import {
  GetScheduleByLcvIdDto,
  GetScheduleByPersonsDto,
  ScheduleCoreService,
  UpdateManyScheduleDto,
  UpdateScheduleById,
} from 'vac-nest-schedule';
import { GetInjectionScheduleVaccineDto, TransformInjectionSchedule } from '../dto';
import { STATUS_CALENDAR, STATUS_CALENDAR_FOR_SCHEDULE, STATUS_PAYMENT } from '../enum';
import { APPOINTMENT_SOURCE } from '../enum/index';
import { InjectionScheduleUltil } from './injectionSchedule.ultil';
import { PIMAppService } from 'vac-nest-pim-app';

@Injectable()
export class InjectionScheduleVaccineService {
  constructor(
    private readonly vaccineHistoryService: VacHistoryService,
    private readonly vaccineScheduleService: ScheduleCoreService,
    private readonly injectionScheduleUltil: InjectionScheduleUltil,
    private readonly pimAppService: PIMAppService,
  ) {}

  /**
   * @TODO lấy danh sách lịch tiêm
   * @param body personId : string
   * @returns
   */
  async getInjectionSchedule(body: GetInjectionScheduleVaccineDto) {
    const { personId } = body;

    try {
      const [scheduleHistory, schedulePresent] = await Promise.all([
        this._getScheduleFromHistory(personId),
        this._getScheduleFromSchedule(personId),
      ]);

      return scheduleHistory.concat(schedulePresent);
    } catch (error) {
      return [];
    }
  }

  /**
   * @TODO lấy danh sách lịch tiêm từ history
   * @param body personId
   */
  async _getScheduleFromHistory(personId: string) {
    const vaccineScheduleHistory = await this.vaccineHistoryService.getByPerson({ personId: personId });
    let arrScheduleHistory: TransformInjectionSchedule[] = [];
    if (_.isEmpty(vaccineScheduleHistory)) return arrScheduleHistory;

    vaccineScheduleHistory?.map((schedule) => {
      const itemSchedule: TransformInjectionSchedule = {
        id: schedule?.id,
        appointmentDate: schedule?.vaccinatedDate,
        sku: schedule?.sku ?? '',
        taxonomies: schedule?.taxonomies ?? '',
        manufactor: schedule?.manufactor ?? '',
        injections: schedule?.injection ?? 0,
        vaccineName: schedule?.vaccineName ?? '',
        shopName: schedule?.shopName ?? '',
        shopCode: schedule?.shopCode ?? '',
        statusAppointment: STATUS_CALENDAR.DA_TIEM,
        statusPayment: STATUS_PAYMENT.DA_THANH_TOAN,
        customerNote: '',
        createdByName: '',
        note: '',
        isScheduleFromHistory: true,
        isPaid: true,
      };
      arrScheduleHistory = [...arrScheduleHistory, itemSchedule];
    });

    return arrScheduleHistory;
  }

  /**
   * @TODO lấy danh sách lịch tiêm từ schedule tương lai
   * @param body personId
   */
  async _getScheduleFromSchedule(personId: string) {
    const { items } = await this.vaccineScheduleService.getScheduleByPerson({ personId: personId });

    let arrSchedule: TransformInjectionSchedule[] = [];

    if (items.length === 0) return arrSchedule;

    items?.map((injection) => {
      if (injection.status === STATUS_CALENDAR_FOR_SCHEDULE.Da_Hen) {
        const status_payment = injection?.isPaid ? STATUS_PAYMENT.DA_THANH_TOAN : STATUS_PAYMENT.CHUA_THANH_TOAN;
        const schedule: TransformInjectionSchedule = {
          id: injection?.id,
          appointmentDate: injection?.appointmentDate,
          injections: injection?.injections ?? null,
          manufactor: injection?.manufactor ?? '',
          vaccineName: injection?.skuName ?? '',
          taxonomies: injection?.taxonomies ?? '',
          sku: injection?.sku ?? '',
          shopName: injection?.shopName ?? '',
          shopCode: injection?.shopCode ?? '',
          statusPayment: status_payment,
          statusAppointment: injection?.status,
          createdByName: injection?.createdByName ?? '',
          customerNote: injection?.customerNote ?? '',
          note: injection?.note ?? '',
          isScheduleFromHistory: false,
          isPaid: injection?.isPaid,
        };
        arrSchedule = [...arrSchedule, schedule];
      }
    });

    return arrSchedule;
  }

  /**
   * @description updated schedule
   */
  async updateScheduleById(id: string, body: UpdateScheduleById) {
    const data = await this.vaccineScheduleService.updateScheduleById(id, body);
    return data;
  }

  async updateManySchedule(body: UpdateManyScheduleDto) {
    const { items } = body;
    const data = await this.vaccineScheduleService.updateManySchedule(items);
    if (data?.length === 0)
      return {
        items: [],
      };

    let arrSchedule: TransformInjectionSchedule[] = [];
    data?.map((injection) => {
      const status_payment = injection?.orderDetailAttachmentCode
        ? STATUS_PAYMENT.DA_THANH_TOAN
        : STATUS_PAYMENT.CHUA_THANH_TOAN;
      const schedule = {
        id: injection?.id,
        appointmentDate: injection?.appointmentDate,
        injections: injection?.injections ?? null,
        manufactor: injection?.manufactor ?? '',
        vaccineName: injection?.skuName ?? '',
        taxonomies: injection?.taxonomies ?? '',
        sku: injection?.sku ?? '',
        shopName: injection?.shopName ?? '',
        shopCode: injection?.shopCode ?? '',
        statusPayment: status_payment,
        statusAppointment: injection?.status,
        createdByName: injection?.createdByName ?? '',
        customerNote: injection?.customerNote ?? '',
        updatedByName: injection?.updatedByName ?? '',
        updatedBy: injection?.updatedBy ?? '',
        note: injection?.note ?? '',
        isScheduleFromHistory: false,
        waittingPaid: false,
      };
      arrSchedule = [...arrSchedule, schedule] as any;
    });

    return {
      items: arrSchedule,
    };
  }

  /**
   * @TODO lấy danh sách lịch tiêm có người liên hệ
   * @param body personId : string[]
   * @returns []
   */
  async getScheduleVaccinePersons(body: GetScheduleByPersonsDto) {
    try {
      const [scheduleHistory, schedulePresent] = await Promise.all([
        this.getScheduleHistoryByPersons(body),
        this.getScheduleByPersons(body),
      ]);
      const res = scheduleHistory.concat(schedulePresent);
      // get pim để gắn thêm isMultiDose
      const arrSku = res.map((item) => item.sku);
      const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(arrSku);
      res.map((item) => {
        const product = listProduct.find((productEntry) => productEntry.sku === item.sku);
        item['isMultiDose'] = product?.isMultiDose || false;
        item['unitCodeSale'] = item['unitCodeSale'] || null;
        item['unitNameSale'] = item['unitNameSale'] || null;
      });
      return res;
    } catch (error) {
      return [];
    }
  }

  // lịch hẹn (schedule) có người liên hệ
  async getScheduleByPersons(body: GetScheduleByPersonsDto) {
    const { skipCount = 0, personIds, status } = body;
    let skip = skipCount;
    const fixedPerPage = 70;
    const items = [];

    const { totalCount } = await this.vaccineScheduleService.getScheduleByPersons({
      skipCount: 0,
      maxResultCount: fixedPerPage,
      personIds,
      status,
    });
    const fetchTimes = Math.ceil(totalCount / fixedPerPage);

    for (let i = 0; i < fetchTimes; i++) {
      const { items: data } = await this.vaccineScheduleService.getScheduleByPersons({
        skipCount: skip,
        maxResultCount: fixedPerPage,
        personIds: personIds,
        status,
      });
      if (data.length) {
        items.push(...data);
      }
      skip += fixedPerPage;
    }

    if (items?.length === 0) return [];

    const resSchedules = this.injectionScheduleUltil._transformFieldScheduleInjection({
      items,
      typeAppomentSource: APPOINTMENT_SOURCE.Lich_Hen,
    });

    return resSchedules;
  }

  // lịch sử hẹn tiêm có người liên hệ
  async getScheduleHistoryByPersons(body: GetHistoryScheduleByPersonsDto) {
    let arrScheduleHistory: TransformInjectionSchedule[] = [];

    const resSchedulesHistory = await this.vaccineHistoryService.getHistoryScheduleByPersons(body);

    if (resSchedulesHistory?.length === 0) return [];
    resSchedulesHistory?.map(({ personId, history }) => {
      const res = this.injectionScheduleUltil._transformFieldScheduleInjection({
        items: history,
        typeAppomentSource: APPOINTMENT_SOURCE.Lich_Su,
        personId,
      });
      arrScheduleHistory = [...arrScheduleHistory, ...res];
    });

    return arrScheduleHistory;
  }

  /**
   * 20-03-2024
   * @TODO lấy danh sách lịch hẹn ở màn tư vấn
   */

  async getListScheduleByLcvId(body: GetScheduleByLcvIdDto) {
    const { skipCount = 0, lcvId } = body;
    let skip = skipCount;
    const fixedPerPage = 70;
    const items = [];

    const { totalCount } = await this.vaccineScheduleService.getScheduleByPersonCodeV2({
      skipCount: skip,
      maxResultCount: fixedPerPage,
      personCode: lcvId,
    });
    const fetchTimes = Math.ceil(totalCount / fixedPerPage);

    for (let i = 0; i < fetchTimes; i++) {
      const { items: data } = await this.vaccineScheduleService.getScheduleByPersonCodeV2({
        skipCount: skip,
        maxResultCount: fixedPerPage,
        personCode: lcvId,
      });
      if (data.length) {
        items.push(...data);
      }
      skip += fixedPerPage;
    }

    if (items?.length === 0) return [];

    return (
      this.injectionScheduleUltil._transformFieldScheduleInjection({
        items,
        typeAppomentSource: APPOINTMENT_SOURCE.Lich_Hen,
      }) || []
    );
  }
}
