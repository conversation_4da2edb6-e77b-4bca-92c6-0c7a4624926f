APP_NAME=vaccine-schedule-engine-v2
PORT=3005
NODE_ENV=CI
APP_VERSION=1.0.0

# Elastic logger
ES_LOG_DISABLED=false
ES_LOG_LEVEL=info
ES_LOG_PREFIX=logs-ci-vaccine-schedule-engine-v2
ES_LOG_NODE=https://elk-vac.frt.vn
ES_LOG_USER=logs-dev
ES_LOG_PASSWORD=tngQYFoEO5gWTsprDMNL
ES_LOG_RETRY_LIMIT=5
ES_LOG_FLUSH_INTERVAL=1000
ES_LOG_HEALTH_CHECK_TIMEOUT=30s
ES_LOG_BUFFERING=true
ES_LOG_BUFFER_LIMIT=10000

MONGODB_URI=********************************************************************************************************************

# APM tracing
ELASTIC_APM_SERVER_URL=http://vac-apm.frt.vn
ELASTIC_APM_SECRET_TOKEN=
ELASTIC_APM_SERVER_NAME=vaccine-schedule-engine-v2
ELASTIC_APM_OPENTELEMETRY_BRIDGE_ENABLED=true
ELASTIC_APM_ENV=Vac-CI

VAC_HISTORY_URL=http://ci-vac-history-v2-api.frt.local/
VAC_SCHEDULE_URL=http://ci-vac-schedule-v2-api.frt.local/
EXAMINATION_URL=http://ci-vac-examination.frt.local/

# INVENTORY
INVENTORY_URL=http://ci-kong-gw.lc.frt.local/lc-inventory-service/

# REGIMEN
REGIMEN_URL=http://ci-vac-regimen-application-v2-api.frt.local/

# PIM
PIM_APP_URL=http://ci-vac-pim-product-api.frt.local/

# Family
FAMILY_URL=http://ci-vac-family-profile-v2.frt.local/

IMS_URL=http://ci-ims-inventory-api.lc.frt.local/
IMS_BOOKING_URL=http://ci-ims-booking-api.lc.frt.local/
IMS_PRODUCT_ITEM_URL=http://ci-product-item-api.lc.frt.local/
INVENTORY_HISTORY_URL=http://ci-ims-inventory-history.lc.frt.local/

# GHC CDS ENGINE
GHC_CDS_ENGINE_URL=http://ci-kong-dhp-service.dhp.frt.local/dhp-vac-fhir-cds-engine/
ORDER_RULE_ENGINE_URL=http://ci-vac-order-rule-engine.frt.local/

FROM_DATE='2025-01-27'
TO_DATE='2025-01-31'