import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ClassResponse } from '@app/utils/utilities/class-response';
import { CustomHeaders, generalSchema } from '@app/utils/utilities/function-swagger';
import { GetS3PresignDto, GetS3PresignResponse } from '../dto/get-s3-presign.dto';
import { GetS3UploadLinkDto, GetS3UploadLinkResponse } from '../dto/get-s3-upload-link.dto';
import { FilesService } from '../services/files.service';

@Controller({ path: 'files', version: '1' })
@ApiTags('Files')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(ClassResponse, GetS3UploadLinkResponse, GetS3PresignResponse)
@CustomHeaders()
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Get('upload-links')
  @ApiOperation({
    summary: 'Lấy link để upload hình ảnh (Pre-signed urls)',
  })
  @ApiOkResponse({
    description: 'Trả về thông tin filter',
    schema: generalSchema(GetS3UploadLinkResponse, 'object'),
  })
  getS3UploadLinks(@Query() getS3UploadLinkDto: GetS3UploadLinkDto) {
    return this.filesService.getS3UploadLinks(getS3UploadLinkDto);
  }

  @Get('presign-links')
  @ApiOperation({
    summary: 'Lấy link Publish',
  })
  @ApiOkResponse({
    description: 'Trả về thông tin filter',
    schema: generalSchema(GetS3PresignResponse, 'object'),
  })
  getS3Presign(@Query() getS3PresignDto: GetS3PresignDto) {
    return this.filesService.getS3Presign(getS3PresignDto);
  }
}
