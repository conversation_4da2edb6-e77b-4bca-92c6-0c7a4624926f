import { CanActivate, ExecutionContext, HttpStatus, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import JWTDecode from 'jwt-decode';
import { Observable } from 'rxjs';
import { IS_PUBLIC_KEY } from '../decorators';
import { ErrorCode } from '../errors/error-code';
import { SystemException } from '../exceptions';
import { IError } from '../interfaces';
import { IAuthUser } from '../interfaces/auth-user.interface';
import { pick } from 'lodash';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    request.headers = pick(request.headers, ['shop-code', 'shop-name', 'order-channel', 'authorization']);
    const isPublic = this.reflector.getAllAndOverride(IS_PUBLIC_KEY, [context.getHandler()]);
    if (isPublic) {
      return true;
    }
    const token = request?.headers?.authorization || request?.headers?.Authorization || '';
    if (!token) {
      const exception: IError = {
        code: ErrorCode.UNAUTHORIZED,
        message: ErrorCode.getError(ErrorCode.UNAUTHORIZED),
        details: ErrorCode.getError(ErrorCode.UNAUTHORIZED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.UNAUTHORIZED);
    }
    const jwtDecode = JWTDecode<IAuthUser>(token);
    request.user = jwtDecode;
    request.token = token;
    return true;
  }
}
