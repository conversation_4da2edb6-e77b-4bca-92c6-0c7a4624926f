{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/vaccine-schedule-engine-v2/src", "deleteOutDir": true, "compilerOptions": {"plugins": ["@nestjs/swagger/plugin"], "webpack": true, "tsConfigPath": "apps/vaccine-schedule-engine-v2/tsconfig.app.json"}, "monorepo": true, "root": "apps/vaccine-schedule-engine-v2", "projects": {"vaccine-schedule-engine-v2": {"type": "app", "root": "apps/vaccine-schedule-engine-v2", "entryFile": "main", "sourceRoot": "apps/vaccine-schedule-engine-v2/src", "compilerOptions": {"tsConfigPath": "apps/vaccine-schedule-engine-v2/tsconfig.app.json"}}, "vaccine-web-schedule-engine": {"type": "app", "root": "apps/vaccine-web-schedule-engine", "entryFile": "main", "sourceRoot": "apps/vaccine-web-schedule-engine/src", "compilerOptions": {"tsConfigPath": "apps/vaccine-web-schedule-engine/tsconfig.app.json"}}, "modules": {"type": "library", "root": "libs/modules", "sourceRoot": "libs/modules", "entryFile": "index", "compilerOptions": {"tsConfigPath": "libs/modules/tsconfig.lib.json"}}, "utils": {"type": "library", "root": "libs/utils", "sourceRoot": "libs/utils", "entryFile": "index", "compilerOptions": {"tsConfigPath": "libs/utils/tsconfig.lib.json"}}}}