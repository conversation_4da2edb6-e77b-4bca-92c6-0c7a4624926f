import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { plainToInstance } from 'class-transformer';
import { CheckModel, SuggestModel, ValidateModel } from '../models';
import { TableName } from '../constants';

@Injectable()
export class MongooseService {
  constructor(
    @InjectModel(SuggestModel.name)
    private readonly suggestModel: Model<SuggestModel>,
    @InjectModel(ValidateModel.name)
    private readonly validateModel: Model<ValidateModel>,
    @InjectModel(CheckModel.name)
    private readonly checkModel: Model<CheckModel>,
  ) {}

  async bulkCreateWithTransaction<T>(modelT: Model<T>, data: T[]): Promise<T[]> {
    try {
      const createdDocuments = await modelT.insertMany(data);
      return createdDocuments;
    } catch (error) {
      throw error;
    }
  }

  async handleAddData<T>(key: string, data: T[]) {
    if (key.includes(TableName.SUGGEST)) {
      const bulkDataDto: SuggestModel[] = plainToInstance(SuggestModel, data, {
        exposeUnsetFields: false,
        excludeExtraneousValues: true,
      });
      await this.bulkCreateWithTransaction<SuggestModel>(this.suggestModel, bulkDataDto);
    }

    if (key.includes(TableName.VALIDATE)) {
      const bulkDataDto: ValidateModel[] = plainToInstance(ValidateModel, data, {
        exposeUnsetFields: false,
        excludeExtraneousValues: true,
      });
      await this.bulkCreateWithTransaction<ValidateModel>(this.validateModel, bulkDataDto);
    }

    if (key.includes(TableName.CHECK)) {
      const bulkDataDto: CheckModel[] = plainToInstance(CheckModel, data, {
        exposeUnsetFields: false,
        excludeExtraneousValues: true,
      });
      await this.bulkCreateWithTransaction<CheckModel>(this.checkModel, bulkDataDto);
    }
  }
}
