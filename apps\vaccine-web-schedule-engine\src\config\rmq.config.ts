import { Injectable } from '@nestjs/common';
import { registerAs } from '@nestjs/config';
import { ClientProvider, ClientsModuleOptionsFactory, Transport } from '@nestjs/microservices';

export default registerAs('rmqConfig', () => ({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 5432,
  urls: [
    {
      hostname: process.env.RABBIT_MQ_HOST,
      port: process.env.RABBIT_MQ_PORT || 5672,
      username: process.env.RABBIT_MQ_USER,
      password: process.env.RABBIT_MQ_PASSWORD,
      vhost: process.env.RABBIT_MQ_VIRTUAL_HOST,
    },
  ],
  queue: process.env.RABBIT_MQ_ORDER_DEPOSIT_QUEUE,
  queueOptions: {
    durable: true,
  },
  prefetchCount: 1,
}));

@Injectable()
export class RabbitMQConfig implements ClientsModuleOptionsFactory {
  createClientOptions(): ClientProvider {
    return {
      transport: Transport.RMQ,
      options: {
        urls: [
          {
            hostname: process.env.RABBIT_MQ_HOST,
            port: +process.env.RABBIT_MQ_PORT || 5672,
            username: process.env.RABBIT_MQ_USER,
            password: process.env.RABBIT_MQ_PASSWORD,
            vhost: process.env.RABBIT_MQ_VIRTUAL_HOST,
          },
        ],
        queue: process.env.RABBIT_MQ_ORDER_DEPOSIT_QUEUE,
        queueOptions: {
          durable: true,
        },
        prefetchCount: 1,
      },
    };
  }
}

export const RSA_RMQ_URI = `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}:${process.env.RABBIT_MQ_PORT}/${process.env.RABBIT_MQ_VIRTUAL_HOST}`;
