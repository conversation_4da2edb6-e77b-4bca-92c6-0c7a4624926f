import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class versions1675392692804 implements MigrationInterface {
  private tableName = 'versions';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isUnique: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'version',
            type: 'character varying',
          },
          {
            name: 'status',
            type: 'int',
            default: 1,
          },
          {
            name: 'type',
            type: 'int',
            default: 1,
          },
          {
            name: 'version_code',
            type: 'character varying',
          },
          {
            name: 'note',
            type: 'character varying',
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamp with time zone',
            default: 'now()',
          },
          {
            name: 'deleted_at',
            type: 'timestamp with time zone',
            isNullable: true,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
