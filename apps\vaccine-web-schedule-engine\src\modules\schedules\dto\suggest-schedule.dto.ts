import { ApiProperty, PickType } from '@nestjs/swagger';
import { CurrentDto, FutureDto } from './common';
import { Expose, Transform, Type } from 'class-transformer';
import { IsOptional, IsString, ValidateNested } from 'class-validator';
import _ from 'lodash';
import { OrderInjectionStatus } from 'vac-nest-order-injection';
import moment from 'moment';
import { GUID_EMPTY } from 'apps/vaccine-schedule-engine-v2/src/constants';
import { DiseaseGroupIdInCart } from './extra.dto';
import { ScreenType } from '../constants';
import { GetManyByLcvRes, VaccineHistoryDetailDto } from 'vac-nest-history';
import { GetPersonByIdRes } from 'vac-nest-family';

export class PersonRedisDto extends PickType(GetPersonByIdRes, [
  'lcvId',
  'from',
  'to',
  'gender',
  'dateOfBirth',
  'id',
  'ageUnitCode',
]) {}

export class ItemScheduleHistoryRedisDto extends PickType(VaccineHistoryDetailDto, [
  'id',
  'regimenId',
  'sku',
  'isRegimenClose',
  'vaccinatedDate',
  'vaccineName',
  'diseaseGroupId',
]) {
  @ApiProperty()
  @Expose()
  @IsOptional()
  status?: number;
}

export class scheduleHistoriesDto extends PickType(GetManyByLcvRes, ['lcvId']) {
  @ApiProperty({ isArray: true, type: ItemScheduleHistoryRedisDto })
  @Expose()
  @IsOptional()
  @Type(() => ItemScheduleHistoryRedisDto)
  history?: ItemScheduleHistoryRedisDto[];
}

export class SuggestScheduleDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @Type(() => CurrentDto)
  @ValidateNested({ each: true })
  @Transform(({ value, obj }) => {
    let isChange = false;

    value = _.sortBy(value, [
      (z: CurrentDto) => {
        const formatDate = moment(z['oldDate'] || z['date'] || '9999-12-31')
          .utcOffset(7)
          .format('YYYY-MM-DD');
        console.log('formatDate', formatDate);
        return moment(formatDate).valueOf();
      },
      'priority',
    ]);
    _.forEach(value, (item) => {
      item['screenType'] = obj?.screenType || '';
      if (item['isChangeDate']) {
        isChange = true;
        return;
      }
      if (isChange && item['status'] !== OrderInjectionStatus.Da_tiem) item['date'] = '';
    });
    value = value?.filter((current) => !(!current?.regimenId || current?.regimenId === GUID_EMPTY));
    return value;
  })
  arrCurrent: Array<CurrentDto>;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @Type(() => FutureDto)
  @ValidateNested({ each: true })
  arrFuture: Array<FutureDto>;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  expectedDate: Date;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value || 'INDICATION')
  screenType?: ScreenType;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  ticketCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  listDiseaseGroupIdInCart?: DiseaseGroupIdInCart[];

  @ApiProperty({ isArray: true, type: scheduleHistoriesDto })
  @Expose()
  @IsOptional()
  @Type(() => scheduleHistoriesDto)
  scheduleHistories?: scheduleHistoriesDto[];

  @ApiProperty({ isArray: true, type: PersonRedisDto })
  @Expose()
  @IsOptional()
  @Type(() => PersonRedisDto)
  persons?: PersonRedisDto[];
}

export class SuggestScheduleRes {
  @ApiProperty()
  @Expose()
  @Type(() => CurrentDto)
  arrCurrent: Array<CurrentDto>;

  @ApiProperty()
  @Expose()
  @Type(() => FutureDto)
  arrFuture: Array<FutureDto>;
}
