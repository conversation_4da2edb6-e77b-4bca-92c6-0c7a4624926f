import { Module } from '@nestjs/common';
import { FamilyModule } from 'vac-nest-family';
import { VacHistoryModule } from 'vac-nest-history';
import { PIMAppModule } from 'vac-nest-pim-app';
import { RegimenModule } from 'vac-nest-regimen';
import { SchedulesController } from './controllers/schedules.controller';
import { SchedulesWebService } from './services/schedules.service';
import { IMSModule } from 'vac-nest-ims';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { IMSBookingModule } from 'vac-nest-ims-booking';
import { OsrModule } from 'vac-nest-osr';
import { ScheduleCoreModule } from 'vac-nest-schedule';
import { OrderRuleEngineModule } from 'vac-nest-order-rule-engine';
import { ScheduleRulesService } from './services/schedule-rules.service';
import { RuleMaxInjectionService } from './services/maxInjection-rule.service';
import { AgeRuleService } from './services/age-rule.service';
import { SkuLimitRuleService } from './services/sku-limit-rule.service';

@Module({
  imports: [
    IMSModule,
    FamilyModule,
    RegimenModule,
    PIMAppModule,
    VacHistoryModule,
    ExaminationCoreModule,
    IMSBookingModule,
    OsrModule,
    ScheduleCoreModule,
    OrderRuleEngineModule,
  ],
  controllers: [SchedulesController],
  providers: [SchedulesWebService, ScheduleRulesService, RuleMaxInjectionService, AgeRuleService, SkuLimitRuleService],
  exports: [],
})
export class SchedulesWebModule {}
