import { GetQuotaLimitedRes } from 'vac-nest-osr/dist/dto/get-quota-limit.dto';
import { RuleDistanceItem } from 'vac-nest-regimen';

export class DiseaseGroupIdInCart {
  diseaseGroupId: string;
  quantity: number;
}

export class ExtraDto {
  listDiseaseGroupIdInCart?: DiseaseGroupIdInCart[];
  osrLimitedQuota?: GetQuotaLimitedRes[];
  timesBuy?: number;
  arrDiseaseGroupIdDefined?: string[] = [];
  vaccineConversion?: { [key: string]: RuleDistanceItem[] };
  isRuleConversion?: boolean;
  dateConversionLatest?: Date;
}
