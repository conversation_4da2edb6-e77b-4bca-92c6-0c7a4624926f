import { ArgumentsHost, BadRequestException, Catch, ExceptionFilter, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { ErrorCode } from '../errors/error-code';
import { IError, IErrorResponse } from '../interfaces';

@Catch(BadRequestException)
export class BadRequestExceptionFilter implements ExceptionFilter {
  catch(exception: BadRequestException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse() as any;

    const commonErrorPayload = {
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    };

    try {
      const error: IError = {
        code: ErrorCode.BAD_REQUEST,
        message: ErrorCode.getError(ErrorCode.BAD_REQUEST),
        details: exceptionResponse?.message?.toString(),
        validationErrors:
          typeof exceptionResponse?.message === 'object'
            ? exceptionResponse?.message?.map((e: string) => ({
                message: e,
                members: [e.split(' ').at(0)],
              }))
            : null,
      };

      const httpErrorResponse: IErrorResponse = {
        coreUrl: '',
        status: status,
        error: error,
        ...commonErrorPayload,
      };
      // elk logging errors
      Logger.error(
        {
          message: 'HttpException',
          fields: {
            info: `${request.method}: ${request.originalUrl} - ${httpErrorResponse.status} - ${0}ms`,
            method: request.method,
            url: request.originalUrl,
            bodyReq: JSON.stringify(request.body || {}),
            queryReq: JSON.stringify(request.query || {}),
            paramsReq: JSON.stringify(request.params || {}),
            headers: JSON.stringify(request.headers || {}),
            status: httpErrorResponse.status,
            timeExecuted: 0,
            unitTime: 'ms',
            dataRes: JSON.stringify(httpErrorResponse),
          },
        },
        'HttpException',
        false,
      );

      response.status(httpErrorResponse.status).json(httpErrorResponse);
    } catch (ex) {
      Logger.error(ex);
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(commonErrorPayload);
    }
  }
}
