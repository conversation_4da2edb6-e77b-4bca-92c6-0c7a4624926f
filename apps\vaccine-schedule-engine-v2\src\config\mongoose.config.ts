import { Injectable, Logger } from '@nestjs/common';
import { MongooseModuleOptions, MongooseOptionsFactory } from '@nestjs/mongoose';
import { MongooseError } from 'mongoose';

@Injectable()
export class MongooseConfigService implements MongooseOptionsFactory {
  createMongooseOptions(): MongooseModuleOptions {
    return {
      uri: process.env.MONGODB_URI,
      minPoolSize: 1,
      maxPoolSize: 5,
      retryAttempts: 5,
      retryDelay: 2000,
      connectionErrorFactory: (error: MongooseError) => {
        Logger.error({
          message: 'MongoError',
          fields: {
            info: 'MongoError',
            method: '',
            url: 'mongoDB',
            status: 500,
            headers: '',
            bodyReq: '',
            paramsReq: '',
            timeExecuted: 0,
            unitTime: 'ms',
            dataRes: typeof error !== 'string' ? JSON.stringify(error) : error,
          },
        });
        console.error(error);
        return error;
      },
      connectionFactory: (connection) => {
        return connection;
      },
    };
  }
}
