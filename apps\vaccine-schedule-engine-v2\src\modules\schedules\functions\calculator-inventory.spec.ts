import { handleCalculatorInventory } from '.';

describe('Calculator Inventory', () => {
  it('Tồn lơn hơn lịch hẹn 1 đơn vị - Tồn 10, hẹn 8', () => {
    const sku = '00000001';
    const stock = {
      sku: sku,
      quantity: 1,
      quantityAvailable: 10,
      quantityOrder: 0,
      whsCode: '010',
      whsName: '',
      unitCode: 2,
      unitName: 'Lọ',
      unitLevel: 2,
      quantityExchange: 8,
    };
    const arrStock = [
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 10,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 2,
        unitName: 'Lọ',
        unitLevel: 2,
        quantityExchange: 8,
      },
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 80,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 3,
        unitName: 'Liều',
        unitLevel: 3,
        quantityExchange: 8,
      },
    ];
    const scheduleFind = {
      unitCodeSale: 2,
      count: 8,
      measureUnitName: 'string',
      ratio: 8,
      level: 2,
    };
    const countSchedule = [
      {
        unitCodeSale: 2,
        count: 8,
        measureUnitName: 'string',
        ratio: 8,
        level: 2,
      },
      {
        unitCodeSale: 3,
        count: 0,
        measureUnitName: 'string',
        ratio: 0,
        level: 3,
      },
    ];
    handleCalculatorInventory(
      sku,
      arrStock,
      stock,
      {
        countSchedule,
      },
      scheduleFind,
    );
    expect(stock.quantityAvailable).toEqual(2);
  });

  it('Tồn lớn hơn lịch hẹn đúng 1 đơn vị và không có hẹn đơn vị nhỏ hơn - Tồn 9 lọ, hẹn 8 lọ 0 liều', () => {
    const sku = '00000001';
    const stock = {
      sku: sku,
      quantity: 1,
      quantityAvailable: 9,
      quantityOrder: 0,
      whsCode: '010',
      whsName: '',
      unitCode: 2,
      unitName: 'Lọ',
      unitLevel: 2,
      quantityExchange: 8,
    };
    const arrStock = [
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 9,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 2,
        unitName: 'Lọ',
        unitLevel: 2,
        quantityExchange: 8,
      },
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 72,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 3,
        unitName: 'Liều',
        unitLevel: 3,
        quantityExchange: 8,
      },
    ];
    const scheduleFind = {
      unitCodeSale: 2,
      count: 8,
      measureUnitName: 'string',
      ratio: 8,
      level: 2,
    };
    const countSchedule = [
      {
        unitCodeSale: 2,
        count: 8,
        measureUnitName: 'string',
        ratio: 8,
        level: 2,
      },
      {
        unitCodeSale: 3,
        count: 0,
        measureUnitName: 'string',
        ratio: 0,
        level: 3,
      },
    ];
    handleCalculatorInventory(
      sku,
      arrStock,
      stock,
      {
        countSchedule,
      },
      scheduleFind,
    );
    expect(stock.quantityAvailable).toEqual(1);
  });

  it('Tồn lớn hơn lịch hẹn đúng 1 đơn vị và có hẹn đơn vị nhỏ hơn - Tồn 9 lọ, hẹn 8 lọ 1 liều', () => {
    const sku = '00000001';
    const stock = {
      sku: sku,
      quantity: 1,
      quantityAvailable: 9,
      quantityOrder: 0,
      whsCode: '010',
      whsName: '',
      unitCode: 2,
      unitName: 'Lọ',
      unitLevel: 2,
      quantityExchange: 8,
    };
    const arrStock = [
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 9,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 2,
        unitName: 'Lọ',
        unitLevel: 2,
        quantityExchange: 8,
      },
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 72,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 3,
        unitName: 'Liều',
        unitLevel: 3,
        quantityExchange: 8,
      },
    ];
    const scheduleFind = {
      unitCodeSale: 2,
      count: 8,
      measureUnitName: 'string',
      ratio: 8,
      level: 2,
    };
    const countSchedule = [
      {
        unitCodeSale: 2,
        count: 8,
        measureUnitName: 'string',
        ratio: 8,
        level: 2,
      },
      {
        unitCodeSale: 3,
        count: 1,
        measureUnitName: 'string',
        ratio: 0,
        level: 3,
      },
    ];
    handleCalculatorInventory(
      sku,
      arrStock,
      stock,
      {
        countSchedule,
      },
      scheduleFind,
    );
    expect(stock.quantityAvailable).toEqual(0);
  });

  it('Tồn bằng lịch hẹn', () => {
    const sku = '00000001';
    const stock = {
      sku: sku,
      quantity: 1,
      quantityAvailable: 8,
      quantityOrder: 0,
      whsCode: '010',
      whsName: '',
      unitCode: 2,
      unitName: 'Lọ',
      unitLevel: 2,
      quantityExchange: 8,
    };
    const arrStock = [
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 8,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 2,
        unitName: 'Lọ',
        unitLevel: 2,
        quantityExchange: 8,
      },
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 64,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 3,
        unitName: 'Liều',
        unitLevel: 3,
        quantityExchange: 8,
      },
    ];
    const scheduleFind = {
      unitCodeSale: 2,
      count: 8,
      measureUnitName: 'string',
      ratio: 8,
      level: 2,
    };
    const countSchedule = [
      {
        unitCodeSale: 2,
        count: 8,
        measureUnitName: 'string',
        ratio: 8,
        level: 2,
      },
      {
        unitCodeSale: 3,
        count: 1,
        measureUnitName: 'string',
        ratio: 0,
        level: 3,
      },
    ];
    handleCalculatorInventory(
      sku,
      arrStock,
      stock,
      {
        countSchedule,
      },
      scheduleFind,
    );
    expect(stock.quantityAvailable).toEqual(0);
  });

  it('Tồn nhỏ hơn lịch hẹn', () => {
    const sku = '00000001';
    const stock = {
      sku: sku,
      quantity: 1,
      quantityAvailable: 7,
      quantityOrder: 0,
      whsCode: '010',
      whsName: '',
      unitCode: 2,
      unitName: 'Lọ',
      unitLevel: 2,
      quantityExchange: 8,
    };
    const arrStock = [
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 7,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 2,
        unitName: 'Lọ',
        unitLevel: 2,
        quantityExchange: 8,
      },
      {
        sku: sku,
        quantity: 1,
        quantityAvailable: 56,
        quantityOrder: 0,
        whsCode: '010',
        whsName: '',
        unitCode: 3,
        unitName: 'Liều',
        unitLevel: 3,
        quantityExchange: 8,
      },
    ];
    const scheduleFind = {
      unitCodeSale: 2,
      count: 8,
      measureUnitName: 'string',
      ratio: 8,
      level: 2,
    };
    const countSchedule = [
      {
        unitCodeSale: 2,
        count: 8,
        measureUnitName: 'string',
        ratio: 8,
        level: 2,
      },
      {
        unitCodeSale: 3,
        count: 1,
        measureUnitName: 'string',
        ratio: 0,
        level: 3,
      },
    ];
    handleCalculatorInventory(
      sku,
      arrStock,
      stock,
      {
        countSchedule,
      },
      scheduleFind,
    );
    expect(stock.quantityAvailable).toEqual(0);
  });
});
