# Vaccine Schedule Engine V2 Architecture

## Project Structure

The project follows a modular architecture based on NestJS framework. Here is the overall structure:

```
src/
├── app.module.ts            # Main application module
├── common/                  # Common utilities, decorators, filters, guards, etc.
│   ├── decorators/          # Custom decorators
│   ├── exceptions/          # Custom exception handlers
│   ├── filters/             # Exception filters
│   ├── guards/              # Authentication guards
│   ├── interceptors/        # Request/Response interceptors
│   ├── interfaces/          # Common interfaces
│   └── validations/         # Validation utilities
├── config/                  # Configuration modules
│   ├── rmq.config.ts        # RabbitMQ configuration
│   └── ...
├── constants/               # Application constants and enums
│   ├── app.constant.ts      # Global constants
│   ├── order-channels.constant.ts # Order channel definitions
│   └── index.ts
├── docs/                    # Documentation files
├── main.ts                  # Application entry point
├── modules/                 # Feature modules
│   ├── health/              # Health check module
│   ├── injection-schedule-vaccine/ # Injection schedules module
│   └── schedules/           # Main schedules module
│       ├── adapters/        # Abstract classes and adapters
│       ├── constants/       # Module-specific constants
│       ├── controllers/     # API controllers
│       ├── dto/             # Data Transfer Objects
│       ├── functions/       # Utility functions
│       ├── models/          # Database models
│       └── services/        # Business logic services
├── swagger/                 # Swagger documentation
├── tracing.ts              # Distributed tracing configuration
└── utilities/              # Utility functions
```

## Module Structure

Each feature module typically follows this structure:

1. **Controllers**: Handle HTTP requests and delegate business logic to services
2. **Services**: Implement business logic and interact with adapters/repositories
3. **DTOs**: Define data structures for request/response payloads
4. **Models**: Define database schemas (for MongoDB)
5. **Adapters**: Abstract classes that define common interfaces
6. **Constants**: Module-specific constants and enums

## Key Architectural Patterns

### 1. Dependency Injection

NestJS's built-in DI container is used extensively. Services are injected into controllers and other services.

Example:

```typescript
@Injectable()
export class SchedulesService extends SchedulesAdapter {
  constructor(
    private readonly familyService: FamilyService,
    private readonly imsService: IMSService, // other dependencies...
  ) {
    super(/* ... */);
  }
}
```

### 2. Adapter Pattern

Abstract classes define interfaces that concrete implementations must follow.

Example:

```typescript
@Injectable()
export abstract class SchedulesAdapter {
  abstract preProcessData(suggestScheduleDto: SuggestScheduleDto, shopCode: string): Promise<PreProcessDataRes>;
}
```

### 3. DTO Pattern

Data Transfer Objects are used to validate and sanitize request/response data.

Example:

```typescript
export class PreProcessCurrentDto extends CurrentDto {
  person?: GetPersonByIdRes;
  stock?: StockMedicRes;
  isStockInTicket?: boolean;
  // other properties...
}
```

## Code Conventions

### Naming Conventions

1. **Files**: Use kebab-case for filenames (e.g., `schedule-rules.service.ts`)
2. **Classes**: Use PascalCase for class names (e.g., `ScheduleRulesService`)
3. **Methods/Functions**: Use camelCase (e.g., `preProcessData()`)
4. **Variables**: Use camelCase (e.g., `arrCurrentSku`)
5. **Constants**: Use UPPER_SNAKE_CASE for global constants (e.g., `API_PREFIX`)
6. **Interfaces/Types**: Use PascalCase with prefix 'I' for interfaces (e.g., `IErrorResponse`)
7. **Enums**: Use PascalCase (e.g., `RegimenRulesType`)

### File Organization

1. **One class per file**: Each class should be in its own file
2. **Index files**: Use barrel exports (`index.ts`) to simplify imports
3. **Related files**: Group related files in the same directory

### Code Style

The project uses ESLint and Prettier for code formatting:

1. **Indentation**: 2 spaces
2. **Line length**: Maximum 120 characters
3. **Quotes**: Single quotes
4. **Semicolons**: Required
5. **Trailing commas**: Always use trailing commas
6. **Arrow functions**: Always use parentheses for parameters

Configuration can be found in `.eslintrc.js` and `.prettierrc`.

### Imports

1. Organize imports in the following order:

   - Node.js built-in modules
   - External packages/libraries
   - Internal modules/files

2. Avoid using absolute paths; use relative paths for imports

### Documentation

1. Use JSDoc comments for public APIs, services, and complex functions
2. Document parameters, return types, and exceptions
3. Include example usage where appropriate

### Error Handling

1. Use custom exception classes for domain-specific errors
2. Use NestJS exception filters for handling and formatting errors
3. Include appropriate HTTP status codes and error messages

### Testing

1. Write unit tests for services and utilities
2. Use Jest as the testing framework
3. Aim for high test coverage, especially for business logic

## Data Flow

1. **HTTP Request** → Controller
2. Controller validates input using DTOs
3. Controller delegates to Service
4. Service processes request using business logic
5. Service returns data to Controller
6. Controller formats and returns HTTP Response

## External Dependencies

The application integrates with several external services:

1. **MongoDB**: Primary database
2. **RabbitMQ**: Message broker for asynchronous communication
3. **Other microservices**: Family, PIM, Regimen, etc.

## CI/CD Pipeline

The project uses a GitLab CI/CD pipeline with the following stages:

1. **Build**: Compile TypeScript to JavaScript
2. **Test**: Run unit and integration tests
3. **Lint**: Check code style and formatting
4. **Build Docker**: Create Docker image
5. **Deploy**: Deploy to various environments (Dev, QA, Production)

## Version Control

1. Follow the Git Flow branching model
2. Use conventional commits for commit messages
3. Create merge requests for code reviews
