import { Injectable, Logger } from '@nestjs/common';
import moment from 'moment';
import { calculateTimeDifference, diffDate } from 'apps/vaccine-schedule-engine-v2/src/utilities/function-common';
import { AgeRuleAdapter } from '../adapters/age-rule.abstract';
import {
  RegimenRulesType,
  TEXT_RULE_NOT_OLD_AGE_DANGER,
  TEXT_RULE_NOT_OLD_AGE_WARING,
  TEXT_RULE_OVER_AGE_DANGER,
  TEXT_RULE_OVER_AGE_WARING,
  UnitFromCode,
  UnitFromName,
} from '../constants';
import { RuleRes } from '../dto/common';
import { PreProcessCurrentDto } from '../dto/pre-process-data.dto';
import { RuleType } from 'apps/vaccine-schedule-engine-v2/src/constants';

@Injectable()
export class AgeRuleService extends AgeRuleAdapter {
  isNotOldAge(date: Date, current: PreProcessCurrentDto, rules: RuleRes[]): boolean {
    const filterByRuleType = this.filterRegimenRuleByType(current, RegimenRulesType.THIEU_TUOI);
    Logger.log(`[Data rule chưa đủ tuổi]: ${JSON.stringify(filterByRuleType)}`);
    if (!filterByRuleType) return false;

    const { ageFromValue, ageFromUnitCode, equalFromAge, regimentRules } = filterByRuleType;
    Logger.log(`[Regimen Rule có RuleType = 2]: ${JSON.stringify(regimentRules)}`);

    // tính lịch hẹn thấp nhất theo phác đồ chuẩn
    const minDateRegimen = this.addDateInAge(
      new Date(current?.person?.dateOfBirth),
      ageFromValue,
      ageFromUnitCode,
      equalFromAge,
    );
    Logger.log(`[Thời gian phác đồ min]: ${moment(minDateRegimen).utcOffset(7).format('YYYY-MM-DD')}`);
    // tính ngày/tháng/tuổi của person
    const calculateAge = calculateTimeDifference(new Date(current?.person?.dateOfBirth));
    Logger.log(
      `Thông tin KH => Ngày sinh: ${current?.person?.dateOfBirth} - Thông tin tính tuổi: ${JSON.stringify(
        calculateAge,
      )}`,
    );

    // tính khoảng cách của ngày thấp nhất tới ngày hẹn
    const calculateDistanceDate = diffDate(minDateRegimen, date, 'days');
    Logger.log(
      `[Khoảng cách từ ngày hẹn tới min phác đồ]: ${calculateDistanceDate} - equalFromAge: ${equalFromAge} - minDateRegimen: ${minDateRegimen} - hẹn: ${date}`,
    );
    if (calculateDistanceDate >= 0 && equalFromAge) return false; // equalFromAge === true cho phép bằng với ngày min
    if (calculateDistanceDate > 0 && !equalFromAge) return false; // equalFromAge === false cho phép không bằng với ngày min

    // rule bằng tuổi min và equalFromAge false
    // if (calculateDistanceDate === 0 && !equalFromAge) {
    //   const diffDobWithDate = diffDate(new Date(current?.person?.dateOfBirth), date, 'days') + 1;

    //   Logger.log(`[Rule][Khách hàng chưa đủ tuổi tiêm theo phác đồ]: ${calculateDistanceDate} - ${equalFromAge}`);
    //   const message = TEXT_RULE_NOT_OLD_AGE_DANGER?.replace('{order}', current?.orderInjections?.toString())
    //     ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
    //     ?.replace('{day}', '1')
    //     ?.replace('{unit}', 'ngày');

    //   rules.push({
    //     text: message,
    //     type: 'DANGER',
    //     value: diffDobWithDate,
    //     ruleType: 1,
    //     ruleName: 'Khách hàng chưa đủ tuổi tiêm theo phác đồ',
    //     needReview: true,
    //     isAllowEvaluate: true,
    //     ageUnitCode: 0, // ngày
    //     equalAge: false,
    //   });
    //   return true;
    // }

    if (!regimentRules?.length) return false;
    for (const item of regimentRules) {
      // tính khoảng cách thêm 1 lần nữa dựa vào unit trong regiment rule
      const calculateDistanceDateByUnitFrom = diffDate(date, minDateRegimen, UnitFromCode[item?.fromUnit]);
      Logger.log(
        `[RegimentRules][Khoảng cách ngày hẹn đến min phác đồ]: ${calculateDistanceDateByUnitFrom} - unit: ${item?.fromUnit} - hẹn: ${date} - min: ${minDateRegimen}`,
      );

      if (calculateDistanceDateByUnitFrom <= 0) return;
      // có from và to khác 0
      // check trong khoàng
      if (item?.from && item?.to) {
        if (
          (item?.equalFrom &&
            item?.equalTo &&
            calculateDistanceDateByUnitFrom >= item?.from &&
            calculateDistanceDateByUnitFrom <= item?.to) ||
          (item?.equalFrom &&
            !item?.equalTo &&
            calculateDistanceDateByUnitFrom >= item?.from &&
            calculateDistanceDateByUnitFrom < item?.to) ||
          (!item?.equalFrom &&
            item?.equalTo &&
            calculateDistanceDateByUnitFrom > item?.from &&
            calculateDistanceDateByUnitFrom <= item?.to) ||
          (!item?.equalFrom &&
            !item?.equalTo &&
            calculateDistanceDateByUnitFrom > item?.from &&
            calculateDistanceDateByUnitFrom < item?.to)
        ) {
          Logger.log(
            `[Rule][Khách hàng chưa đủ tuổi tiêm theo phác đồ thuộc trong khoảng]:, Mũi thứ: ${current?.orderInjections} - Phác đồ: ${current?.regimen?.vaccine?.name} - Khoảng cách: ${calculateDistanceDateByUnitFrom} - Unit: ${item?.fromUnit} - AlertLevel: ${item?.alertLevel}`,
          );
          // alertLevel = 1 (waring) | 2 (danger)
          const message =
            item?.alertLevel === 1
              ? TEXT_RULE_NOT_OLD_AGE_WARING?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitFrom.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit])
              : TEXT_RULE_NOT_OLD_AGE_DANGER?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitFrom.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit]);

          rules.push({
            text: message,
            type: item?.alertLevel === 1 ? 'WARNING' : 'DANGER',
            value: ageFromValue,
            ruleType: RuleType.ChuaDuTuoi,
            ruleName: 'Khách hàng chưa đủ tuổi tiêm theo phác đồ',
            needReview: item?.alertLevel === 1 ? false : true,
            isAllowEvaluate: item?.alertLevel === 1 ? false : true,
            ageUnitCode: ageFromUnitCode,
            equalAge: equalFromAge,
          });
          return true;
        }
      }
      // có from và to là vô cực
      // check lớn hơn
      if (item?.from && !item?.to) {
        if (
          (item?.equalFrom && calculateDistanceDateByUnitFrom >= item?.from) ||
          (!item?.equalFrom && calculateDistanceDateByUnitFrom > item?.from)
        ) {
          Logger.log(
            `[Rule][Khách hàng chưa đủ tuổi tiêm theo phác đồ thuộc ngoài khoảng từ ${item?.from} đến vô cực]:, Mũi thứ: ${current?.orderInjections} - Phác đồ: ${current?.regimen?.vaccine?.name} - Khoảng cách: ${calculateDistanceDateByUnitFrom} - Unit: ${item?.fromUnit} - AlertLevel: ${item?.alertLevel}`,
          );
          // alertLevel = 1 (waring) | 2 (danger)
          const message =
            item?.alertLevel === 1
              ? TEXT_RULE_NOT_OLD_AGE_WARING?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitFrom.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit])
              : TEXT_RULE_NOT_OLD_AGE_DANGER?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitFrom.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit]);

          rules.push({
            text: message,
            type: item?.alertLevel === 1 ? 'WARNING' : 'DANGER',
            value: ageFromValue,
            ruleType: RuleType.ChuaDuTuoi,
            ruleName: 'Khách hàng chưa đủ tuổi tiêm theo phác đồ',
            needReview: item?.alertLevel === 1 ? false : true,
            isAllowEvaluate: item?.alertLevel === 1 ? false : true,
            ageUnitCode: ageFromUnitCode,
            equalAge: equalFromAge,
          });
          return true;
        }
      }
    }
    return false;
  }

  isOverAge(date: Date, current: PreProcessCurrentDto, rules?: RuleRes[]): boolean {
    const filterByRuleType = this.filterRegimenRuleByType(current, RegimenRulesType.QUA_TUOI);
    Logger.log(`[Data rule quá tuổi]: ${JSON.stringify(filterByRuleType)}`);
    if (!filterByRuleType) return false;

    const { ageToValue, ageToUnitCode, equalToAge, regimentRules } = filterByRuleType;
    Logger.log(`[Regimen Rule có RuleType = 3]: ${JSON.stringify(regimentRules)}`);
    // tính lịch hẹn cao nhất của phác đồ chuẩn
    let maxDateRegimen = this.addDateInAge(new Date(current?.person?.dateOfBirth), ageToValue, ageToUnitCode);
    if (!equalToAge) {
      maxDateRegimen = new Date(moment(maxDateRegimen).subtract(1, 'days').format());
    }
    Logger.log(`[Thời gian phác đồ max]: ${moment(maxDateRegimen).utcOffset(7).format('YYYY-MM-DD')}`);
    // tính ngày/tháng/tuổi của person

    // tính khoảng cách của ngày thấp nhất tới ngày hẹn
    const calculateDistanceDate = diffDate(date, maxDateRegimen, 'days');
    Logger.log(
      `[Khoảng cách từ ngày hẹn tới max phác đồ]: ${calculateDistanceDate} - equalToAge: ${equalToAge} - maxDateRegimen: ${maxDateRegimen} - hẹn: ${date}`,
    );
    if (calculateDistanceDate >= 0 && equalToAge) return false; // equalFromAge === true cho phép bằng với ngày min
    if (calculateDistanceDate > 0 && !equalToAge) return false; // equalFromAge === false cho phép không bằng với ngày min

    // rule bằng tuổi min và equalFromAge false
    // if (calculateDistanceDate === 0 && !equalToAge) {
    //   Logger.log(`[Rule][Khách hàng quá tuổi tiêm theo phác đồ]: ${calculateDistanceDate} - ${equalToAge}`);
    //   const message = TEXT_RULE_OVER_AGE_DANGER?.replace('{order}', current?.orderInjections?.toString())
    //     ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
    //     ?.replace('{day}', '1')
    //     ?.replace('{unit}', 'ngày');

    //   rules.push({
    //     text: message,
    //     type: 'DANGER',
    //     value: 1,
    //     ruleType: 2,
    //     ruleName: 'Khách hàng quá tuổi tiêm theo phác đồ',
    //     needReview: true,
    //     isAllowEvaluate: true,
    //     ageUnitCode: 0, // ngày
    //     equalAge: false,
    //   });
    //   return true;
    // }

    if (!regimentRules?.length) return false;
    for (const item of regimentRules) {
      // tính khoảng cách thêm 1 lần nữa dựa vào unit trong regiment rule
      const calculateDistanceDateByUnitTo = diffDate(maxDateRegimen, date, UnitFromCode[item?.toUnit]);

      Logger.log(
        `[RegimentRules][Khoảng cách ngày hẹn đến max phác đồ]: ${calculateDistanceDateByUnitTo} - unit: ${item?.toUnit} - hẹn: ${date} - min: ${maxDateRegimen}`,
      );
      if (calculateDistanceDateByUnitTo <= 0) return;
      // có from và to khác 0
      // check trong khoàng
      if (item?.from && item?.to) {
        if (
          (item?.equalFrom &&
            item?.equalTo &&
            calculateDistanceDateByUnitTo >= item?.from &&
            calculateDistanceDateByUnitTo <= item?.to) ||
          (item?.equalFrom &&
            !item?.equalTo &&
            calculateDistanceDateByUnitTo >= item?.from &&
            calculateDistanceDateByUnitTo < item?.to) ||
          (!item?.equalFrom &&
            item?.equalTo &&
            calculateDistanceDateByUnitTo > item?.from &&
            calculateDistanceDateByUnitTo <= item?.to) ||
          (!item?.equalFrom &&
            !item?.equalTo &&
            calculateDistanceDateByUnitTo > item?.from &&
            calculateDistanceDateByUnitTo < item?.to)
        ) {
          Logger.log(
            `[Rule][Khách hàng quá tuổi tiêm theo phác đồ thuộc trong khoảng]:, Mũi thứ: ${current?.orderInjections} - Phác đồ: ${current?.regimen?.vaccine?.name} - Khoảng cách: ${calculateDistanceDateByUnitTo} - Unit: ${item?.toUnit} - alertLevel: ${item?.alertLevel}`,
          );
          // alertLevel = 1 (waring) | 2 (danger)
          const message =
            item?.alertLevel === 1
              ? TEXT_RULE_OVER_AGE_WARING?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitTo.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit])
              : TEXT_RULE_OVER_AGE_DANGER?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitTo.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit]);
          rules.push({
            text: message,
            type: item?.alertLevel === 1 ? 'WARNING' : 'DANGER',
            value: ageToValue,
            ruleType: RuleType.QuaTuoi,
            ruleName: 'Khách hàng chưa đủ tuổi tiêm theo phác đồ',
            isAllowEvaluate: item?.alertLevel === 1 ? false : true,
            needReview: item?.alertLevel === 1 ? false : true,
            ageUnitCode: ageToUnitCode,
            equalAge: equalToAge,
          });
          return true;
        }
      }
      // có from và to là vô cực
      // check lớn hơn
      if (item?.from && !item?.to) {
        if (
          (item?.equalFrom && calculateDistanceDateByUnitTo >= item?.from) ||
          (!item?.equalFrom && calculateDistanceDateByUnitTo > item?.from)
        ) {
          Logger.log(
            `[Rule][Khách hàng quá tuổi tiêm theo phác đồ thuộc ngoài khoảng từ ${item?.from} đến vô cực]:, Mũi thứ: ${current?.orderInjections} - Phác đồ: ${current?.regimen?.vaccine?.name} - Khoảng cách: ${calculateDistanceDateByUnitTo} - Unit: ${item?.toUnit} - alertLevel: ${item?.alertLevel}`,
          );
          // alertLevel = 1 (waring) | 2 (danger)
          const message =
            item?.alertLevel === 1
              ? TEXT_RULE_OVER_AGE_WARING?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitTo.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit])
              : TEXT_RULE_OVER_AGE_DANGER?.replace('{order}', current?.orderInjections?.toString())
                  ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                  ?.replace('{day}', calculateDistanceDateByUnitTo.toString())
                  ?.replace('{unit}', UnitFromName[item?.fromUnit]);
          rules.push({
            text: message,
            type: item?.alertLevel === 1 ? 'WARNING' : 'DANGER',
            value: 0,
            ruleType: RuleType.QuaTuoi,
            ruleName: 'Khách hàng quá tuổi tiêm theo phác đồ',
            isAllowEvaluate: item?.alertLevel === 1 ? false : true,
            needReview: item?.alertLevel === 1 ? false : true,
            ageUnitCode: ageToUnitCode,
            equalAge: equalToAge,
          });
          return true;
        }
      }
    }
    return false;
  }
}
