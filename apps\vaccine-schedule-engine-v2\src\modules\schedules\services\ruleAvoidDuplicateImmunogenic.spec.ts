import { Test, TestingModule } from '@nestjs/testing';
import { ScheduleRulesService } from './schedule-rules.service';
import { AgeRuleService } from './age-rule.service';
import { RuleType } from '../../../constants/app.constant';
import { PreProcessCurrentDto, PreProcessFutureDto } from '../dto/pre-process-data.dto';
import { RuleRes } from '../dto/common';

describe('ScheduleRulesService - ruleAvoidDuplicateImmunogenic', () => {
  let service: ScheduleRulesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleRulesService,
        {
          provide: AgeRuleService,
          useValue: {
            isNotOldAge: jest.fn(),
            isOverAge: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ScheduleRulesService>(ScheduleRulesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return false when current vaccine has no interactions', () => {
    const current = {
      sku: 'SKU001',
      regimen: {
        vaccine: {
          name: 'Test Vaccine',
          vaccineInteractions: [],
        },
      },
      screenType: 'INDICATION',
    } as PreProcessCurrentDto;

    const arrFutureFilter = [] as PreProcessFutureDto[];
    const rules: RuleRes[] = [];

    const result = service.ruleAvoidDuplicateImmunogenic(current, arrFutureFilter, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });

  it('should return false when current vaccine has no interactions with type 5', () => {
    const current = {
      sku: 'SKU001',
      regimen: {
        vaccine: {
          name: 'Test Vaccine',
          vaccineInteractions: [
            {
              sku: 'SKU002',
              interactionType: 4,
              warningType: 0,
              minDistanceValue: 28,
            },
          ],
        },
      },
      screenType: 'INDICATION',
    } as PreProcessCurrentDto;

    const arrFutureFilter = [] as PreProcessFutureDto[];
    const rules: RuleRes[] = [];

    const result = service.ruleAvoidDuplicateImmunogenic(current, arrFutureFilter, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });

  it('should return false when interaction type 5 sku is not found in future list', () => {
    const current = {
      sku: 'SKU001',
      regimen: {
        vaccine: {
          name: 'Test Vaccine',
          vaccineInteractions: [
            {
              sku: 'SKU002',
              interactionType: 5,
              warningType: 0,
              minDistanceValue: 28,
            },
          ],
        },
      },
      screenType: 'INDICATION',
    } as PreProcessCurrentDto;

    const arrFutureFilter = [
      {
        sku: 'SKU003',
        regimen: {
          vaccine: {
            name: 'Other Vaccine',
          },
        },
      },
    ] as PreProcessFutureDto[];
    const rules: RuleRes[] = [];

    const result = service.ruleAvoidDuplicateImmunogenic(current, arrFutureFilter, rules);

    expect(result).toBe(false);
    expect(rules).toHaveLength(0);
  });

  it('should return true and add rule when interaction type 5 sku is found in future list', () => {
    const current = {
      sku: 'SKU001',
      regimen: {
        vaccine: {
          name: 'Test Vaccine',
          vaccineInteractions: [
            {
              sku: 'SKU002',
              interactionType: 5,
              warningType: 0,
              minDistanceValue: 28,
            },
          ],
        },
      },
      screenType: 'INDICATION',
    } as PreProcessCurrentDto;

    const arrFutureFilter = [
      {
        sku: 'SKU002',
        regimen: {
          vaccine: {
            name: 'Future Vaccine',
          },
        },
      },
    ] as PreProcessFutureDto[];
    const rules: RuleRes[] = [];

    const result = service.ruleAvoidDuplicateImmunogenic(current, arrFutureFilter, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
    expect(rules[0]).toEqual({
      text: 'Tôi sẽ định nghĩa sau',
      type: 'DANGER',
      value: 28,
      ruleType: RuleType.TuongTac,
      ruleName: 'Tránh trùng lặp miễn dịch Test Vaccine với Future Vaccine',
      isAllowEvaluate: true,
    });
  });

  it('should handle multiple interactions with type 5 and return on first match', () => {
    const current = {
      sku: 'SKU001',
      regimen: {
        vaccine: {
          name: 'Test Vaccine',
          vaccineInteractions: [
            {
              sku: 'SKU002',
              interactionType: 5,
              warningType: 0,
              minDistanceValue: 28,
            },
            {
              sku: 'SKU003',
              interactionType: 5,
              warningType: 1,
              minDistanceValue: 14,
            },
          ],
        },
      },
      screenType: 'INDICATION',
    } as PreProcessCurrentDto;

    const arrFutureFilter = [
      {
        sku: 'SKU002',
        regimen: {
          vaccine: {
            name: 'Future Vaccine 1',
          },
        },
      },
      {
        sku: 'SKU003',
        regimen: {
          vaccine: {
            name: 'Future Vaccine 2',
          },
        },
      },
    ] as PreProcessFutureDto[];
    const rules: RuleRes[] = [];

    const result = service.ruleAvoidDuplicateImmunogenic(current, arrFutureFilter, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
    expect(rules[0].ruleName).toBe('Tránh trùng lặp miễn dịch Test Vaccine với Future Vaccine 1');
  });

  it('should handle SLIDEBAR screenType with WARNING type', () => {
    const current = {
      sku: 'SKU001',
      regimen: {
        vaccine: {
          name: 'Test Vaccine',
          vaccineInteractions: [
            {
              sku: 'SKU002',
              interactionType: 5,
              warningType: 0,
              minDistanceValue: 28,
            },
          ],
        },
      },
      screenType: 'SLIDEBAR',
    } as PreProcessCurrentDto;

    const arrFutureFilter = [
      {
        sku: 'SKU002',
        regimen: {
          vaccine: {
            name: 'Future Vaccine',
          },
        },
      },
    ] as PreProcessFutureDto[];
    const rules: RuleRes[] = [];

    const result = service.ruleAvoidDuplicateImmunogenic(current, arrFutureFilter, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
    expect(rules[0].type).toBe('WARNING');
  });

  it('should handle missing vaccine name in future vaccine', () => {
    const current = {
      sku: 'SKU001',
      regimen: {
        vaccine: {
          name: 'Test Vaccine',
          vaccineInteractions: [
            {
              sku: 'SKU002',
              interactionType: 5,
              warningType: 0,
              minDistanceValue: 28,
            },
          ],
        },
      },
      screenType: 'INDICATION',
    } as PreProcessCurrentDto;

    const arrFutureFilter = [
      {
        sku: 'SKU002',
        vaccineName: 'Future Vaccine Name',
      },
    ] as PreProcessFutureDto[];
    const rules: RuleRes[] = [];

    const result = service.ruleAvoidDuplicateImmunogenic(current, arrFutureFilter, rules);

    expect(result).toBe(true);
    expect(rules).toHaveLength(1);
    expect(rules[0].ruleName).toBe('Tránh trùng lặp miễn dịch Test Vaccine với Future Vaccine Name');
  });
});
