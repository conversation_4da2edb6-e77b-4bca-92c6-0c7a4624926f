import { Injectable } from '@nestjs/common';
import { ItemHistoryScheduleByPersons } from 'vac-nest-history';
import { ItemScheduleByPerson } from 'vac-nest-schedule';
import { TransformInjectionSchedule } from '../dto';
import { APPOINTMENT_SOURCE, STATUS_CALENDAR, STATUS_CALENDAR_FOR_SCHEDULE, STATUS_PAYMENT } from '../enum';

@Injectable()
export class InjectionScheduleUltil {
  /** map field cho lịch sử, lịch hẹn tiêm */
  _transformFieldScheduleInjection(
    payload: {
      items: ItemHistoryScheduleByPersons[] | ItemScheduleByPerson[];
      typeAppomentSource: number;
      personId?: string;
      lcvId?: string;
    }, // typeAppomentSource: nguồn lịch hẹn từ schedule hay history
  ) {
    const { items, typeAppomentSource, personId, lcvId } = payload;

    const arrSchedule: TransformInjectionSchedule[] = [];

    for (let i = 0; i < items.length; i++) {
      // loại bỏ những lịch hẹn đã đến và huỷ của schedule
      if (
        typeAppomentSource === APPOINTMENT_SOURCE.Lich_Hen &&
        items[i]['status'] !== STATUS_CALENDAR_FOR_SCHEDULE.Da_Hen
      )
        continue;

      const isScheduleFromHistory = typeAppomentSource === APPOINTMENT_SOURCE.Lich_Su; // true : từ history | false: từ schedule

      const statusAppointment =
        typeAppomentSource === APPOINTMENT_SOURCE.Lich_Su ? STATUS_CALENDAR.DA_TIEM : items[i]['status']; // trạng thái đã tiêm cho history và đã hẹn cho schedule

      const isPaid = typeAppomentSource === APPOINTMENT_SOURCE.Lich_Su ? true : items[i]['isPaid'];

      const status_payment = isPaid ? STATUS_PAYMENT.DA_THANH_TOAN : STATUS_PAYMENT.CHUA_THANH_TOAN; // trang thái thanh toán hay chưa => true: thanh toán || false: chưa thanh toán

      const statusPayment =
        typeAppomentSource === APPOINTMENT_SOURCE.Lich_Su ? STATUS_PAYMENT.DA_THANH_TOAN : status_payment; // trạng thái thanh toán cho history và schedule

      const schedule: TransformInjectionSchedule = {
        id: items[i]['id'],
        appointmentDate: items[i]['appointmentDate'] || items[i]['vaccinatedDate'],
        injections: items[i]['injections'] || items[i]['injection'],
        manufactor: items[i]['manufactor'] || '',
        vaccineName: items[i]['skuName'] || items[i]['vaccineName'],
        taxonomies: items[i]['taxonomies'] || '',
        sku: items[i]['sku'] || '',
        shopName: items[i]['locationName'] || items[i]['shopName'],
        shopCode: items[i]['shopCode'] || '',
        statusPayment,
        statusAppointment,
        createdByName: items[i]['createdByName'] || '',
        customerNote: items[i]['customerNote'] || '',
        note: items[i]['note'] || '',
        isScheduleFromHistory,
        personId: personId || items[i]['personId'] || '',
        isPaid,
        orderCode: items[i]['orderCode'],
        orderDetailAttachmentId: items[i]['orderDetailAttachmentId'],
        orderDetailAttachmentCode: items[i]['orderDetailAttachmentCode'],
        sourceId: items[i]['sourceId'],
        regimenId: items[i]['regimenId'],
        locationName: items[i]['locationName'] ?? '',
        lcvId: lcvId || items[i]['lcvId'],
        waittingPaid: items[i]['waittingPaid'],
        unitCodeSale: items[i]['unitCodeSale'],
        unitNameSale: items[i]['unitNameSale'],
      };

      arrSchedule.push(schedule);
    }

    return arrSchedule;
  }
}
