import { GetPersonByIdRes } from 'vac-nest-family';
import { StockMedicRes } from 'vac-nest-ims';
import { RegimenItem } from 'vac-nest-regimen';
import { CurrentDto, FutureDto } from './common';
import { ExtraDto } from './extra.dto';

export class PreProcessCurrentDto extends CurrentDto {
  person?: GetPersonByIdRes;
  stock?: StockMedicRes;
  isStockInTicket?: boolean;
  totalInjectionClose?: number;
  totalHistory?: number;
  isRareGood?: boolean;
}

export class PreProcessFutureDto extends FutureDto {
  orderInjections?: number;
  regimen?: RegimenItem;
  person?: GetPersonByIdRes;
}

export class PreProcessDataRes {
  arrCurrent: Array<PreProcessCurrentDto>;
  arrFuture: Array<PreProcessFutureDto>;
  extra?: ExtraDto;
}
