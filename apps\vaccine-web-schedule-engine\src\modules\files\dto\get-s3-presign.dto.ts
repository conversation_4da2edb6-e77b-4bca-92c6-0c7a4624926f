import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { IsString } from 'class-validator';

export class GetS3PresignDto {
  @ApiProperty({ description: '<PERSON>h sách link', isArray: true })
  @IsString({ each: true })
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  urls: string[];
}

export class GetS3PresignResponse {
  @Expose()
  @ApiProperty()
  links: string[];
}
