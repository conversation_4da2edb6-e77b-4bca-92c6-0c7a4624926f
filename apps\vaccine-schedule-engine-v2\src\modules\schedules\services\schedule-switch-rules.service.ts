import { Injectable } from '@nestjs/common';
import { RuleType } from 'apps/vaccine-schedule-engine-v2/src/constants';
import { getRegimenSwitchRuleDto, RegimenService } from 'vac-nest-regimen';
import { ScheduleSwitchRuleAbstract } from '../adapters/schedule-switch-rules.abstract';
import { RuleRes } from '../dto/common';
import { PreProcessCurrentDto, PreProcessFutureDto } from '../dto/pre-process-data.dto';
import { VaccineHistoryDetailDto } from 'vac-nest-history';
import * as _ from 'lodash';

@Injectable()
export class ScheduleSwitchRulesService extends ScheduleSwitchRuleAbstract {
  constructor(private readonly regimenService: RegimenService) {
    super();
  }

  /**
   * Check for vaccine regimen switch rules during the scheduling process.
   * Validates if the current vaccine's regimen differs from a future scheduled dose
   *
   * @param current - Current vaccine information to be administered.
   * @param arrFuture - Array of upcoming vaccine schedules to compare against.
   * @param rules - Output array to which any matching rule will be pushed.
   * @returns void - Updates the `rules` array in place if a switch rule is matched.
   */

  async checkScheduleSwitchRule(
    current: PreProcessCurrentDto,
    arrFuture: PreProcessFutureDto[],
    regimentIsClose: VaccineHistoryDetailDto[],
    rules: RuleRes[],
    switchRules: getRegimenSwitchRuleDto[],
  ): Promise<void> {
    if (current?.status !== 0) return;

    const matchRule = switchRules?.find((rule) => rule?.sku === current?.sku);

    // If no matching rule is found, exit early
    if (!matchRule) return;

    const futureProcess = [...arrFuture, ...regimentIsClose]?.filter((future) => future?.sku === current?.sku);

    if (!futureProcess) return;

    const getEarliestInjection = _.maxBy(futureProcess, (item) => {
      const dateValue = (item as any)?.date || (item as any)?.vaccinatedDate;
      return dateValue ? new Date(dateValue).getTime() : -Infinity;
    });

    if (getEarliestInjection && getEarliestInjection?.regimenId === current?.regimenId) return;

    rules.push({
      text: `Khách hàng đã có lịch tiêm vắc xin ${current?.regimen?.vaccine?.name} theo phác đồ ${getEarliestInjection['regimen']?.scheduleType}, không thể chuyển đổi sang phác đồ ${current?.regimen?.scheduleType}. Vui lòng gặp bác sĩ để được tư vấn và hội chẩn.`,
      type: matchRule?.warningType === 0 ? 'DANGER' : 'WARNING',
      value: 0,
      ruleType: RuleType.ChuyenDoiVacXin,
      ruleName: 'Chuyển đổi phác đồ',
      isAllowEvaluate: current?.screenType === 'SCHEDULE' || current?.screenType === 'INDICATION' ? true : false,
    });
  }
}
