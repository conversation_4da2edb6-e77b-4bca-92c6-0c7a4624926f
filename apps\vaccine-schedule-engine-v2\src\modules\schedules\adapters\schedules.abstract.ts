import { Injectable, Logger } from '@nestjs/common';
import * as _ from 'lodash';
import moment from 'moment';
import { RATE_GOOD_ATTRIBUTE_ID } from 'apps/vaccine-schedule-engine-v2/src/constants';
import { isSameDate } from 'apps/vaccine-schedule-engine-v2/src/utilities/function-common';
import { FamilyService } from 'vac-nest-family';
import { VacHistoryService } from 'vac-nest-history';
import { IMSService, StockMedicRes } from 'vac-nest-ims';
import { IMSBookingService } from 'vac-nest-ims-booking';
import { OrderInjectionStatus } from 'vac-nest-order-injection';
import { OsrService } from 'vac-nest-osr';
import { PIMAppService } from 'vac-nest-pim-app';
import { RegimenService } from 'vac-nest-regimen';
import { CountShopScheduleRes, ScheduleCoreService, WHS_CODE_NORMAL } from 'vac-nest-schedule';
import { arrDiseaseGroupHard } from '../constants';
import { CurrentDto } from '../dto/common';
import { ExtraDto } from '../dto/extra.dto';
import { PreProcessCurrentDto, PreProcessDataRes, PreProcessFutureDto } from '../dto/pre-process-data.dto';
import { SuggestScheduleDto } from '../dto/suggest-schedule.dto';
import { OrderRuleEngineService } from 'vac-nest-order-rule-engine';

@Injectable()
export abstract class SchedulesAdapter {
  constructor(
    protected readonly familyService: FamilyService,
    protected readonly imsService: IMSService,
    protected readonly regimenService: RegimenService,
    protected readonly pimAppService: PIMAppService,
    protected readonly vacHistoryService: VacHistoryService,
    protected readonly imsBookingService: IMSBookingService,
    protected readonly osrService: OsrService,
    protected readonly scheduleCoreService: ScheduleCoreService,
    protected readonly orderRuleEngineService: OrderRuleEngineService,
  ) {}

  async preProcessData(suggestScheduleDto: SuggestScheduleDto, shopCode: string): Promise<PreProcessDataRes> {
    try {
      const ticketCode = suggestScheduleDto?.ticketCode;
      Logger.log('Preprocessing data...');
      let { arrCurrent, arrFuture } = suggestScheduleDto;
      arrCurrent = _.orderBy(
        arrCurrent,
        [
          (z: CurrentDto) => {
            const formatDate = moment(z['oldDate'] || z['date'] || '9999-12-31')
              .utcOffset(7)
              .format('YYYY-MM-DD');
            console.log('formatDate', formatDate);
            return moment(formatDate).valueOf();
          },
          'isPaid',
          'priority',
        ],
        ['asc', 'desc', 'asc'],
      );
      arrFuture = _.sortBy(arrFuture, ['date', 'priority']);
      Logger.log('===================PRE PROCESSING DATA================');

      const arrLcvId = _.uniq(arrCurrent.map((current) => current.lcvId));
      Logger.log(`lcvIds: ${arrLcvId}`);
      const [persons, arrHistory, dataStockTicket] = await Promise.all([
        this.familyService.getManyByLcvId({ lcvId: arrLcvId }),
        this.vacHistoryService.getManyByLcvIds(arrLcvId, true),
        ticketCode ? this.imsBookingService.getBookingTransES(ticketCode) : null,
      ]);
      const bookingTransDetails = dataStockTicket?.[0]?.bookingTransDetails || [];
      const currentRegimenIds = arrCurrent.map((current) => current.regimenId);
      const futureRegimenIds = arrFuture.map((future) => future.regimenId);
      const historyRegimentIds = _.flattenDeep(
        _.compact(arrHistory?.map((e) => e?.history?.map((historyEntry) => historyEntry.regimenId))),
      );
      const regimenIds = _.uniq([...currentRegimenIds, ...futureRegimenIds, ...historyRegimentIds]);
      if (!regimenIds.length) throw new Error('No regimen found in current, future and history');

      Logger.log(`RegimenIds: ${regimenIds}`);
      const regimens = await this.regimenService.getRegimenByIds({
        regimenIds: _.uniq(regimenIds),
      });
      const arrCurrentSku = arrCurrent.map((current) => current.sku);
      const arrSku = _.compact(_.uniq([...arrCurrentSku]));
      Logger.log(`arrSku: ${arrSku}`);
      if (!arrSku.length) throw new Error('No sku found in current and future');

      const { listProduct: products } = await this.pimAppService.getListProductBySkuNoRule(arrSku);

      const arrSkuRareGoods: string[] = [];
      products?.forEach((e) => {
        if (e?.attributeShop?.attributeOptionId === RATE_GOOD_ATTRIBUTE_ID) {
          arrSkuRareGoods.push(e?.sku);
        }
      });

      const stockRes: Array<StockMedicRes> = [];
      const countSchedule: CountShopScheduleRes[] = [];
      if (shopCode) {
        const [{ inventories }, schedules] = await Promise.all([
          this.imsService.getListStockMedicAtShop({
            skuCodes: arrSku,
            shopCodes: [shopCode],
          }),
          arrSkuRareGoods?.length
            ? this.scheduleCoreService.countShopSchedule({ shopCode, listSkus: arrSkuRareGoods })
            : [],
        ]);

        countSchedule.push(...schedules);
        stockRes.push(...inventories);
      }

      const arrFutureSkuAndDate: Array<{ sku: string; date: Date }> = arrFuture.map((future) => ({
        sku: future.sku,
        date: future.date,
      }));

      /**
       * @TODO Lấy thông tin mũi đã tiêm.
       */

      arrHistory
        ?.at(0)
        ?.history?.filter((h) => h.sku && !h?.isRegimenClose)
        ?.filter((h) => {
          const futureFind = arrFutureSkuAndDate.find(
            (f) => f.sku === h.sku && isSameDate(new Date(f.date), new Date(h.vaccinatedDate)),
          );
          if (!futureFind) return h;
        })
        .forEach((h) => {
          Logger.log('===================HISTORY PUSH================');
          Logger.log(`DATA: ${JSON.stringify(h)}`);
          const regimenFind = regimens.find((r) => r.id === h.regimenId);
          arrFuture.push({
            date: h.vaccinatedDate,
            regimenId: h?.regimenId || regimenFind?.id || '',
            vaccineName: h?.vaccineName || '',
            sku: h.sku,
            status: OrderInjectionStatus.Da_tiem,
            diseaseGroupId: h?.diseaseGroupId || regimenFind?.diseaseGroupId || '',
          });
        });
      Logger.log('===================END================');
      const diseaseGroupCurrentCheck = [];
      const preProcessDataRes = new PreProcessDataRes();

      const diseaseGroupFutureCheck = [];
      // Push history => Update date
      arrFuture = _.sortBy(arrFuture, ['date', 'priority']);

      preProcessDataRes.arrFuture = arrFuture.map((future) => {
        const regimen = regimens.find((r) => r.id === future.regimenId);
        const diseaseGroupIdLength = diseaseGroupFutureCheck.filter((d) => d === future.diseaseGroupId);
        diseaseGroupFutureCheck.push(future.diseaseGroupId);
        return {
          ...future,
          orderInjections: diseaseGroupIdLength?.length + 1,
          regimen,
        };
      });

      // xử lý tính mũi max cho phác đồ đóng
      // công length phác đồ đóng với mũi hiện tại
      const regimentIsClose = arrHistory?.at(0)?.history?.filter((h) => h.sku && h?.isRegimenClose) || [];

      for (const current of arrCurrent) {
        const person = persons.find((p) => p.lcvId === current.lcvId);
        const regimen = regimens.find((r) => r.id === current.regimenId);
        const unitCode =
          current?.unitCode ||
          products.find((p) => p.sku === current.sku)?.measures?.find((m) => m.isSellDefault)?.measureUnitId;
        const stock = {
          ...stockRes.find(
            (s) => s.sku === current.sku && s.unitCode === unitCode && String(s.whsCode).slice(-3) === WHS_CODE_NORMAL,
          ),
        };

        if (current?.status === 0 && stock?.quantityAvailable) {
          const scheduleFind = countSchedule?.find((e) => e?.sku === current?.sku);
          const countScheduleFind = scheduleFind?.countSchedule?.find((e) => e?.unitCodeSale === unitCode);

          const calculatorStock = await this.orderRuleEngineService.calculatorStockRareGood({
            currentSku: current?.sku,
            stockRes,
            stock,
            scheduleFind,
            countScheduleFind,
          });

          stock.quantityAvailable = calculatorStock?.quantityAvailable || 0;
        }
        const isRareGood = arrSkuRareGoods?.length ? arrSkuRareGoods?.includes(current?.sku) : false;

        regimen['vaccine']['isMultiDose'] = products?.find((e) => e.sku === current.sku)?.isMultiDose || false;
        const diseaseGroupHardFind = arrDiseaseGroupHard.find((e) => e === current?.diseaseGroupId);

        const diseaseGroupIdLength = arrFuture.filter((future) => future.diseaseGroupId === current.diseaseGroupId);
        const diseaseGroupCheckLength = diseaseGroupCurrentCheck.filter((d) => d === current.diseaseGroupId);

        const diseaseGroupIdLengthIsClose =
          regimentIsClose?.filter((f) => {
            if (diseaseGroupHardFind) {
              return arrDiseaseGroupHard.includes(f?.diseaseGroupId);
            }
            return f.diseaseGroupId === current.diseaseGroupId;
          }) || [];

        // phục vụ cho việc tính tổng history show message mũi max
        const listHistory =
          arrHistory?.at(0)?.history?.filter((h) => {
            if (diseaseGroupHardFind) {
              return arrDiseaseGroupHard.includes(h?.diseaseGroupId);
            }
            return h.diseaseGroupId === current.diseaseGroupId;
          }) || [];

        // Mũi đã tiêm thì k tính orderInjection
        let orderInjections = 0;
        if (current.status !== OrderInjectionStatus.Da_tiem) {
          orderInjections = diseaseGroupIdLength?.length + diseaseGroupCheckLength?.length + 1;
          diseaseGroupCurrentCheck.push(current.diseaseGroupId);
        } else {
          // Mũi đã tiêm => lấy trong arrFuture
          const futureFind = preProcessDataRes.arrFuture.find(
            (f) => f.sku === current.sku && isSameDate(new Date(f.date), new Date(current.date)),
          );
          orderInjections = futureFind?.orderInjections || 0;
        }
        if (current?.birthday) {
          person.dateOfBirth = new Date(current.birthday);
        }

        // Check stock already booking for this ticket
        const findSku = bookingTransDetails?.find((item) => item?.sku === current?.sku);
        // Update data current
        current.orderInjections = orderInjections;
        current['stock'] = stock;
        current['person'] = person;
        current.regimen = regimen;
        current['isStockInTicket'] = !!findSku || undefined;
        current['totalInjectionClose'] = diseaseGroupIdLengthIsClose?.length || 0; // tổng mũi phác đồ đóng theo diseaseGroupId
        current['totalHistory'] = listHistory?.length;
        current['isRareGood'] = isRareGood;
      }
      preProcessDataRes.arrCurrent = arrCurrent;
      if (process.env.ENABLE_RULE_LIMIT_QUOTA === 'true') {
        const osrMasterQuota = await this.osrService.getMasterDataLimitedQuota({
          shopCode,
        });

        const arrDiseaseGroupIds: string[] = _.uniq(osrMasterQuota?.map((e) => e.diseaseGroupId));
        const arrDiseaseGroupCurrent: string[] = _.uniq(preProcessDataRes?.arrCurrent?.map((e) => e.diseaseGroupId));
        // Lấy những diseaseGroupIds có trong arrCurrent
        const diseaseGroupFilter = arrDiseaseGroupIds.filter((e) => arrDiseaseGroupCurrent.includes(e));

        if (diseaseGroupFilter?.length) {
          const osrLimitedQuota = await this.osrService.getLimitedQuota(
            { shopCode },
            diseaseGroupFilter.map((e) => ({ diseaseGroupId: e })),
          );

          const extra: ExtraDto = {
            listDiseaseGroupIdInCart: suggestScheduleDto?.listDiseaseGroupIdInCart || [],
            arrDiseaseGroupIdDefined: diseaseGroupFilter,
            osrLimitedQuota: osrLimitedQuota,
            timesBuy: suggestScheduleDto?.arrCurrent?.filter(
              (e) => e.vaccinatedNow === 'LATER' || e.vaccinatedNow === 'NOW',
            )?.length,
          };

          preProcessDataRes.extra = extra;
        }
      }

      const conversionRules = await this.regimenService.getMasterDataRuleDistanceByListSku({
        listSku: [...new Set(preProcessDataRes?.arrCurrent?.map((e) => e.sku))],
      });

      if (preProcessDataRes?.extra && Object.keys(preProcessDataRes?.extra)?.length) {
        preProcessDataRes.extra = {
          ...preProcessDataRes?.extra,
          vaccineConversion: conversionRules || null,
        };
      } else {
        preProcessDataRes.extra = {
          vaccineConversion: conversionRules || null,
        };
      }

      regimentIsClose.forEach((history) => {
        const regimenDetail = regimens?.find((item) => item?.id === history?.regimenId);
        history['regimen'] = regimenDetail;
      });
      preProcessDataRes.regimentIsClose = regimentIsClose || [];
      return preProcessDataRes;
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  async preProcessDataForPricing(suggestScheduleDto: SuggestScheduleDto, shopCode: string): Promise<PreProcessDataRes> {
    try {
      Logger.log('Preprocessing data...');
      let { arrCurrent, arrFuture } = suggestScheduleDto;
      arrCurrent = _.sortBy(arrCurrent, [
        (z: CurrentDto) => {
          const formatDate = moment(z['oldDate'] || z['date'] || '9999-12-31')
            .utcOffset(7)
            .format('YYYY-MM-DD');
          console.log('formatDate', formatDate);
          return moment(formatDate).valueOf();
        },
        'priority',
      ]);
      arrFuture = _.sortBy(arrFuture, ['date', 'priority']);
      Logger.log('===================PRE PROCESSING DATA FOR PRICING================');

      const currentRegimenIds = arrCurrent.map((current) => current.regimenId);
      const futureRegimenIds = arrFuture.map((future) => future.regimenId);
      const regimenIds = _.uniq([...currentRegimenIds, ...futureRegimenIds]);
      if (!regimenIds.length) throw new Error('No regimen found in current, future and history');

      Logger.log(`RegimenIds: ${regimenIds}`);
      const regimens = await this.regimenService.getRegimenByIds({
        regimenIds: _.uniq(regimenIds),
      });
      const arrCurrentSku = arrCurrent.map((current) => current.sku);
      const arrSku = _.compact(_.uniq([...arrCurrentSku]));
      Logger.log(`arrSku: ${arrSku}`);
      if (!arrSku.length) throw new Error('No sku found in current and future');

      const { listProduct: products } = await this.pimAppService.getListProductBySkuNoRule(arrSku);
      const stockRes: Array<StockMedicRes> = [];
      if (shopCode) {
        const { inventories } = await this.imsService.getListStockMedicAtShop({
          skuCodes: arrSku,
          shopCodes: [shopCode],
        });
        stockRes.push(...inventories);
      }

      Logger.log('===================END================');
      const diseaseGroupCurrentCheck = [];
      const preProcessDataRes = new PreProcessDataRes();

      const diseaseGroupFutureCheck = [];
      // Push history => Update date
      arrFuture = _.sortBy(arrFuture, ['date', 'priority']);

      preProcessDataRes.arrFuture = arrFuture.map((future) => {
        const regimen = regimens.find((r) => r.id === future.regimenId);
        const diseaseGroupIdLength = diseaseGroupFutureCheck.filter((d) => d === future.diseaseGroupId);
        diseaseGroupFutureCheck.push(future.diseaseGroupId);
        return { ...future, orderInjections: diseaseGroupIdLength?.length + 1, regimen };
      });

      preProcessDataRes.arrCurrent = arrCurrent.map((current) => {
        const regimen = regimens.find((r) => r.id === current.regimenId);
        const unitCode = products
          .find((p) => p.sku === current.sku)
          ?.measures?.find((m) => m.isSellDefault)?.measureUnitId;
        const stock = stockRes.find(
          (s) =>
            s.sku === current.sku &&
            s.unitCode === (current?.extraData?.['unitCodeSale'] || unitCode) &&
            String(s.whsCode).slice(-3) === WHS_CODE_NORMAL,
        );

        const diseaseGroupIdLength = arrFuture.filter((future) => future.diseaseGroupId === current.diseaseGroupId);
        const diseaseGroupCheckLength = diseaseGroupCurrentCheck.filter((d) => d === current.diseaseGroupId);
        // Mũi đã tiêm thì k tính orderInjection
        let orderInjections = 0;
        if (current.status !== OrderInjectionStatus.Da_tiem) {
          orderInjections = diseaseGroupIdLength?.length + diseaseGroupCheckLength?.length + 1;
          diseaseGroupCurrentCheck.push(current.diseaseGroupId);
        } else {
          // Mũi đã tiêm => lấy trong arrFuture
          const futureFind = preProcessDataRes.arrFuture.find(
            (f) => f.sku === current.sku && isSameDate(new Date(f.date), new Date(current.date)),
          );
          orderInjections = futureFind?.orderInjections || 0;
        }

        return {
          ...current,
          orderInjections: orderInjections,
          stock,
          regimen,
        };
      });

      return preProcessDataRes;
    } catch (error) {
      Logger.error(error);
    }
  }

  groupVaccineBySku(arrCurrent: Array<PreProcessCurrentDto>, expectedDate?: Date): Array<PreProcessCurrentDto> {
    const arrCurrentNew: Array<PreProcessCurrentDto> = [];
    const now = expectedDate ? new Date(expectedDate) : new Date();
    const groupBySku = _.groupBy(arrCurrent, 'sku');
    _.forEach(groupBySku, (current: Array<PreProcessCurrentDto>) => {
      // sortby Date
      current = _.sortBy(current, ['date']);
      let index = 0;
      current.forEach((item) => {
        item.orderInjectionsNew = item.orderInjections;
        item.orderInjections = ++index;
        if (isSameDate(new Date(item.date), now) && item.vaccinatedNow === 'LATER') {
          item.vaccinatedNow = 'NOW';
        } else if (!isSameDate(new Date(item.date), now) && item.vaccinatedNow === 'NOW') {
          item.vaccinatedNow = 'LATER';
        }
        arrCurrentNew.push(item);
      });
    });

    return arrCurrentNew;
  }

  abstract suggestSchedule(suggestScheduleDto: SuggestScheduleDto): Promise<PreProcessDataRes>;
  abstract validateSchedule(suggestScheduleDto: SuggestScheduleDto): Promise<PreProcessDataRes>;
  abstract getDateTheBest(current: PreProcessCurrentDto, arrFuture: Array<PreProcessFutureDto>): Date | string;
}
