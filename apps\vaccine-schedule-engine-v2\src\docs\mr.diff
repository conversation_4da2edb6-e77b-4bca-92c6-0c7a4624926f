diff --git a/src/modules/schedules/services/schedule-rules.service.ts b/src/modules/schedules/services/schedule-rules.service.ts
index 8b12309d..0e531577 100644
--- a/src/modules/schedules/services/schedule-rules.service.ts
+++ b/src/modules/schedules/services/schedule-rules.service.ts
@@ -558,8 +558,10 @@ export class ScheduleRulesService {
 
     _.forEach(groupedData, (arrCurrentDto: Array<PreProcessCurrentDto>) => {
       //get last Item
-      const regimenLast = arrCurrentDto?.at(-1)?.regimen;
-      const itemLast = arrCurrentDto?.at(-1);
+      // Sort by date in descending order and get the most recent regimen
+      const sortedByDate = [...arrCurrentDto].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
+      const regimenLast = sortedByDate[0]?.regimen;
+      const itemLast = sortedByDate[0];
       // check phac do co mui nhac lai hay k?
       const detailRepeatFrequency = regimenLast?.details?.find((e) => e.repeatFrequency);
       Logger.log(`detailRepeatFrequency: ${JSON.stringify(detailRepeatFrequency)}`);
