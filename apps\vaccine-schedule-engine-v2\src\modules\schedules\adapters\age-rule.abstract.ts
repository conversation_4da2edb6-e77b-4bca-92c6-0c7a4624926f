import moment from 'moment';
import { RuleRes } from '../dto/common';
import { PreProcessCurrentDto } from '../dto/pre-process-data.dto';
import { ICommonRule } from '../interfaces/common-rule.interface';
import { TypeUnitFrom } from '../constants';

export abstract class AgeRuleAdapter implements ICommonRule {
  filterRegimenRuleByType(current: PreProcessCurrentDto, type: number) {
    const { orderInjections, regimen, regimenId } = current;
    if (!regimen?.details?.length) return null;

    const findDetailByOrder = regimen?.details?.find((e) => e.order === orderInjections && e?.regimenId === regimenId);
    if (!findDetailByOrder) return null;

    const filterByRegimenRuleType = findDetailByOrder?.regimenRules?.filter((i) => i?.regimenRulesType === type);
    if (!filterByRegimenRuleType?.length) return null;
    return {
      regimentRules: filterByRegimenRuleType || [],
      ageFromValue: findDetailByOrder?.ageFromValue,
      ageFromUnitCode: findDetailByOrder?.ageFromUnitCode,
      equalFromAge: findDetailByOrder?.equalFromAge,
      ageToValue: findDetailByOrder?.ageToValue,
      ageToUnitCode: findDetailByOrder?.ageToUnitCode,
      equalToAge: findDetailByOrder?.equalToAge,
    };
  }
  /**
   * @description cộng ngày/tháng/năm theo định nghĩa
   * @param date
   * @param age
   * @param ageUnitCode
   * @returns
   */
  addDateInAge(date: Date, age: number, ageUnitCode: TypeUnitFrom, equal?: boolean) {
    const newDate = moment(date);
    switch (ageUnitCode) {
      case TypeUnitFrom.DAYS:
        newDate.add(age, 'days');
        break;
      case TypeUnitFrom.WEEK:
        newDate.add(age, 'weeks');
        break;
      case TypeUnitFrom.MONTHS:
        newDate.add(age, 'months');
        break;
      case TypeUnitFrom.AGES:
        newDate.add(age, 'years');
        break;
      default:
        break;
    }
    if (equal === false) {
      newDate.add(1, 'days');
    }
    return new Date(newDate.format());
  }

  /**
   * @description rule chưa đủ tuổi
   * @docs https://reqs.fptshop.com.vn/browse/FV-7543
   */
  abstract isNotOldAge(date: Date, current: PreProcessCurrentDto, rules?: RuleRes[]): boolean;

  /**
   * @description rule quá tuổi
   * @docs https://reqs.fptshop.com.vn/browse/FV-7543
   */
  abstract isOverAge(date: Date, current: PreProcessCurrentDto, rules?: RuleRes[]): boolean;
}
