# =========================================
# GitLab CI/CD Pipeline Configuration
# Phiên bản tối ưu: Triển khai phân tích tự động
# Cập nhật: 2025
# =========================================

# Quy tắc kích hoạt pipeline
workflow:
  rules:
    # Chạy pipeline cho tất cả các nhánh
    # Trigger pipeline khi có merge request vào branch ci hoặc release/xxx
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "ci" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\// || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main-v3")
      when: always
    # Trigger pipeline khi có commit vào source branch của MR đang target tới ci hoặc release/xxx
    - if: $CI_PIPELINE_SOURCE == "push" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "ci" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\// || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main-v3")
      when: always
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - when: never

# Cấu hình mặc định cho tất cả job
default:
  timeout: 30m
  interruptible: true
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  tags:
    - ai

# Biến môi trường toàn cục
variables:
  GIT_STRATEGY: fetch
  GIT_CLEAN_FLAGS: -ffdx
  GIT_CHECKOUT: 'true'

# Các giai đoạn của pipeline
stages:
  - analyze

# Job phân tích toàn diện
AI_Review:
  stage: analyze
  image: python:3.9-slim
  script:
    - |
      mkdir -p .devops

      CONFIG_DIR="/home/<USER>/.devops"
      if [ -d "$CONFIG_DIR" ]; then
        cp -r $CONFIG_DIR/* .devops/ 2>/dev/null || true
        find $CONFIG_DIR -maxdepth 1 -name ".*" -type f -exec cp {} .devops/ \; 2>/dev/null || true
      else
        exit 1
      fi

      if [ ! -f ".devops/pipeline_manager.py" ]; then
        exit 1
      fi

      echo "[$(date '+%Y-%m-%d %H:%M:%S')] Bắt đầu quá trình phân tích mã nguồn"
      python3 .devops/pipeline_manager.py
      status=$?

      if [ $status -eq 0 ]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ✅ Pipeline hoàn thành thành công"
      else
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ❌ Pipeline thất bại - Xem log để biết chi tiết"
        exit 1
      fi
  artifacts:
    paths:
      - scan/
      - .devops/pipeline.log
    reports:
      codequality: scan/sonar-report.json
      secret_detection: scan/gitleaks-report.json
      sast: scan/semgrep-report.json
    expire_in: 2 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "ci" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\// || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main-v3")
      when: always
    - if: $CI_PIPELINE_SOURCE == "push" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "ci" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\// || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main-v3")
      when: always
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  allow_failure: false
  timeout: 30m
