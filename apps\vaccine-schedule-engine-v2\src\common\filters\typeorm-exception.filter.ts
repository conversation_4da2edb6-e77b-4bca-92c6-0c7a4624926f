import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus, Inject, LoggerService } from '@nestjs/common';
import { TypeORMError } from 'typeorm';
import { Request, Response } from 'express';
import { IErrorResponse } from '../interfaces';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';

@Catch(TypeORMError)
export class TypeOrmExceptionFilter implements ExceptionFilter {
  constructor(@Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService) {}

  catch(exception: TypeORMError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const message: string = (exception as TypeORMError).message;
    const code: string = (exception as any).code;
    const typeOrmErrorResponse: IErrorResponse = {
      coreUrl: '',
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      error: {
        code: code,
        message: message,
        details: message,
        validationErrors: null,
      },
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    };
    response.status(typeOrmErrorResponse.status).json(typeOrmErrorResponse);
  }
}
