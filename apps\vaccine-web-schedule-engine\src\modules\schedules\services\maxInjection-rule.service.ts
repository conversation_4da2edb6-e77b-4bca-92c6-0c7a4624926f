import { Injectable } from '@nestjs/common';
import { MaxInjectionRuleAdapter } from '../adapters/maxInjection-rule.abstract';
import { CurrentDto, FutureDto, RuleRes } from '../dto/common';
import { PreProcessCurrentDto } from '../dto/pre-process-data.dto';
import { RuleType } from 'apps/vaccine-schedule-engine-v2/src/constants';
import { arrDiseaseGroupHard } from '../constants';

@Injectable()
export class RuleMaxInjectionService extends MaxInjectionRuleAdapter {
  async checkRuleMaxInjectionDiseaseGroup(
    current: PreProcessCurrentDto,
    arrFuture: FutureDto[],
    rules: RuleRes[],
    arrCurrent?: CurrentDto[],
  ) {
    const diseaseGroupHardFind = arrDiseaseGroupHard.find((e) => e === current?.diseaseGroupId);
    const arrFilterByDiseaseGroup =
      arrFuture?.filter((i) => {
        if (diseaseGroupHardFind) {
          return arrDiseaseGroupHard.includes(i?.diseaseGroupId);
        }
        return i.diseaseGroupId === current?.diseaseGroupId;
      }) || [];

    if (!current?.regimen?.diseaseGroup?.maxInjections) return;

    // filter phác đồ đóng fix cho trường hợp 5trong1 và 6trong1
    // 00000000-0000-0000-0000-000000000000 phác đồ k kinh doanh mới chốt với core và BA
    const totalRegimenNotKDDiffCurrent = arrFuture
      ?.filter(
        (i) =>
          arrDiseaseGroupHard?.includes(i?.diseaseGroupId) &&
          i?.sku &&
          (!i?.regimenId || i?.regimenId === '00000000-0000-0000-0000-000000000000'),
      )
      ?.reduce((accumulator, regimenNotKD) => {
        // tính tổng mũi phác đồ k kinh doanh loại trừ current
        if (
          regimenNotKD?.diseaseGroupId !== current?.diseaseGroupId &&
          arrDiseaseGroupHard?.includes(current?.diseaseGroupId)
        ) {
          accumulator = accumulator + 1;
        }
        return accumulator;
      }, 0);

    const totalCurrent = arrCurrent?.filter((i) => i?.status !== 2)?.length || 0;

    if (
      (arrFilterByDiseaseGroup.length + current?.totalInjectionClose > current?.regimen?.diseaseGroup?.maxInjections &&
        ![2].includes(current?.status)) ||
      current?.totalInjectionClose + current?.orderInjections + totalRegimenNotKDDiffCurrent >
        current?.regimen?.diseaseGroup?.maxInjections
    ) {
      rules.push({
        text: `Nhóm bệnh ${current?.regimen?.diseaseGroup?.name} chỉ được tiêm tối đa ${
          current?.regimen?.diseaseGroup?.maxInjections
        } mũi. Khách hàng có lịch tiêm và lịch sử tiêm tổng cộng ${
          totalCurrent + current?.totalHistory
        } mũi. Vui lòng điều chỉnh hoặc gửi hội chẩn.`,
        value: 0,
        type: current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
        ruleType: RuleType.QuaMuiToiDa,
        ruleName: 'Chỉ định quá số mũi tối đa của một nhóm bệnh',
        isAllowEvaluate: true,
      });
    }
    return true;
  }
}
