import { ClassResponse } from '@app/utils/utilities/class-response';
import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Public } from 'apps/vaccine-schedule-engine-v2/src/common/decorators';
import { CustomHeaders } from 'apps/vaccine-schedule-engine-v2/src/utilities/function-swagger';
import { SchedulesWebService } from '../services/schedules.service';
import { generalSchema } from '@app/utils/utilities/function-swagger';
import { CheckAllRuleSchedulesDto, CheckAllRuleSchedulesRes, SuggestScheduleDto } from '../dto';

@Controller({ path: 'schedules', version: '1' })
@ApiTags('Schedules')
@CustomHeaders()
@ApiExtraModels(ClassResponse, CheckAllRuleSchedulesRes)
export class SchedulesController {
  constructor(private readonly schedulesWebService: SchedulesWebService) {}

  @Post('suggest')
  @Public()
  @HttpCode(HttpStatus.OK)
  async suggest(@Body() suggestScheduleDto: SuggestScheduleDto) {
    return await this.schedulesWebService.suggestSchedule(suggestScheduleDto);
  }

  @Post('validate')
  @Public()
  @HttpCode(HttpStatus.OK)
  async validate(@Body() suggestScheduleDto: SuggestScheduleDto) {
    return await this.schedulesWebService.validateSchedule(suggestScheduleDto);
  }

  @Post('check')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch hẹn',
    schema: generalSchema(CheckAllRuleSchedulesRes, 'object'),
  })
  async checkSchedules(@Body() checkAllRuleSchedulesDto: CheckAllRuleSchedulesDto) {
    return await this.schedulesWebService.checkAllSchedules(checkAllRuleSchedulesDto);
  }

  //   @Post('suggest-for-pricing')
  //   @Public()
  //   @HttpCode(HttpStatus.OK)
  //   async suggestForPricing(@Body() suggestScheduleDto: SuggestScheduleDto) {
  //     return this.schedulesService.suggestScheduleForPricing(suggestScheduleDto);
  //   }
}
