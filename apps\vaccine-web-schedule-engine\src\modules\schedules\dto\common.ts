import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { IsDateString, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { RegimenItem } from 'vac-nest-regimen';
import { ScreenType } from '../constants';

export class RuleRes {
  @ApiProperty()
  @Expose()
  text: string;

  @ApiProperty()
  @Expose()
  value: number;

  @ApiProperty()
  @Expose()
  type: 'WARNING' | 'DANGER';

  @ApiProperty()
  @Expose()
  ruleType?: number;

  @ApiProperty()
  @Expose()
  ruleName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isAllowEvaluate?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  needReview?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  ageUnitCode?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  equalAge?: boolean;
}

export class CurrentDto {
  @ApiProperty()
  @Expose()
  @IsString()
  lcvId: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  priority: number;

  @ApiProperty()
  @Expose()
  @IsString()
  sku: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  regimenId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  date: string;

  @ApiProperty()
  @Expose()
  @IsString()
  diseaseGroupId: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty({ description: 'Field này dành cho ValidateSchedule' })
  @Expose()
  @IsOptional()
  isChangeDate?: boolean;

  @ApiHideProperty()
  @Expose()
  @IsOptional()
  @Transform(({ value }) => {
    if (!value?.length) return [];
    return value?.map((e) => ({
      ...e,
      isAllowEvaluate: e['isAllowEvaluate'] || false,
      needReview: e['needReview'] === false ? false : true,
    }));
  })
  rules?: Array<RuleRes>;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  vaccinatedNow?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  birthday?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  injectionRoute?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  dosage?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  unitName?: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  @IsOptional()
  unitCode?: number;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  orderInjectionId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orderDetailAttachmentId?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orderDetailAttachmentCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  vacOrderCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  sourceId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orderCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  ticketCode?: string;

  @ApiHideProperty()
  @Expose()
  orderInjections?: number;

  @ApiHideProperty()
  @Expose()
  orderInjectionsNew?: number;

  @ApiHideProperty()
  @Expose()
  @IsString()
  @IsOptional()
  screenType?: ScreenType;

  @ApiHideProperty()
  @Expose()
  @IsOptional()
  extraData?: Record<string, any>;

  @ApiHideProperty()
  @Expose()
  @IsOptional()
  regimen?: RegimenItem;
}

export class FutureDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  sku: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  regimenId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  vaccineName?: string;

  @ApiProperty()
  @IsDateString()
  @Expose()
  date: Date;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @IsString()
  @Expose()
  diseaseGroupId: string;
}
