import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { PreProcessCurrentDto } from '../dto/pre-process-data.dto';
import { AgeRuleService } from './age-rule.service';
import { ScheduleRulesService } from './schedule-rules.service';
import * as _ from 'lodash';

// Mock lodash module - using actual lodash and only mocking groupBy
jest.mock('lodash', () => {
  const originalModule = jest.requireActual('lodash');
  return {
    ...originalModule,
    groupBy: jest.fn().mockImplementation((collection) => {
      return { '1-1': collection };
    }),
    forEach: originalModule.forEach,
    negate: originalModule.negate,
  };
});

describe('ScheduleRulesService', () => {
  let service: ScheduleRulesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleRulesService,
        {
          provide: AgeRuleService,
          useValue: {
            isNotOldAge: jest.fn(),
            isOverAge: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ScheduleRulesService>(ScheduleRulesService);

    // Mock Logger
    jest.spyOn(Logger, 'log').mockImplementation(() => undefined);
  });

  describe('addScheduleAppointmentReminder', () => {
    it('should return the original array if no items match the conditions', () => {
      // Arrange
      const arrCurrent: Array<PreProcessCurrentDto> = [
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 1,
          date: '2023-01-01T00:00:00.000Z',
          regimen: {
            details: [{ order: 1 }],
          },
        } as unknown as PreProcessCurrentDto,
      ];

      // Act
      const result = service.addScheduleAppointmentReminder(arrCurrent);

      // Assert
      expect(result).toBe(arrCurrent);
      expect(result.length).toBe(1);
    });

    it('should add a reminder when the conditions are met - sorted by date correctly', () => {
      // Arrange
      const arrCurrent: Array<PreProcessCurrentDto> = [
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 1,
          status: 2, // injected
          date: '2023-01-01T00:00:00.000Z',
          regimen: {
            details: [
              { order: 1 },
              {
                order: 2,
                repeatFrequency: true,
                distanceType: 0,
                distanceValueByType: 30,
                minDistance: 28,
              },
            ],
          },
        } as unknown as PreProcessCurrentDto,
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 2,
          status: 2, // injected
          date: '2023-02-01T00:00:00.000Z', // More recent date
          regimen: {
            details: [
              { order: 1 },
              {
                order: 2,
                repeatFrequency: true,
                distanceType: 0,
                distanceValueByType: 30,
                minDistance: 28,
              },
            ],
          },
        } as unknown as PreProcessCurrentDto,
      ];

      // Mock checkRuleCompletedInjection to return true
      jest.spyOn(service, 'checkRuleCompletedInjection').mockReturnValue(true);

      // Mock addDateInDefined
      jest.spyOn(service, 'addDateInDefined').mockReturnValue(new Date('2023-03-03T00:00:00.000Z'));

      // Act
      const result = service.addScheduleAppointmentReminder(arrCurrent);

      // Assert
      expect(result.length).toBe(3);
      expect(result[2]).toMatchObject({
        diseaseGroupId: 1,
        lcvId: 1,
        sku: 'SKU1',
        orderInjections: 3, // Incremented
        status: 0, // Schedule
        vaccinatedNow: null,
        date: '2023-03-03T00:00:00.000Z',
      });
    });

    it('should use the most recent regimen by date even if not the last in array', () => {
      // Arrange
      const arrCurrent: Array<PreProcessCurrentDto> = [
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 2,
          status: 2, // injected
          date: '2023-02-15T00:00:00.000Z', // Most recent date
          regimen: {
            details: [
              { order: 1 },
              {
                order: 2,
                repeatFrequency: true,
                distanceType: 0,
                distanceValueByType: 30,
                minDistance: 28,
              },
            ],
          },
        } as unknown as PreProcessCurrentDto,
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 1,
          status: 2, // injected
          date: '2023-01-01T00:00:00.000Z',
          regimen: {
            details: [{ order: 1 }, { order: 2 }],
          },
        } as unknown as PreProcessCurrentDto,
      ];

      // Mock checkRuleCompletedInjection to return true
      jest.spyOn(service, 'checkRuleCompletedInjection').mockReturnValue(true);

      // Mock addDateInDefined
      jest.spyOn(service, 'addDateInDefined').mockReturnValue(new Date('2023-03-17T00:00:00.000Z'));

      // Act
      const result = service.addScheduleAppointmentReminder(arrCurrent);

      // Assert
      expect(result.length).toBe(3);
      expect(result[2]).toMatchObject({
        diseaseGroupId: 1,
        lcvId: 1,
        sku: 'SKU1',
        orderInjections: 3, // Incremented from the most recent item
        status: 0,
        date: '2023-03-17T00:00:00.000Z',
      });
    });

    it('should not add reminder if detailRepeatFrequency is not found', () => {
      // Arrange
      const arrCurrent: Array<PreProcessCurrentDto> = [
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 1,
          status: 2, // injected
          date: '2023-01-01T00:00:00.000Z',
          regimen: {
            details: [
              { order: 1 },
              { order: 2 }, // No repeatFrequency
            ],
          },
        } as unknown as PreProcessCurrentDto,
      ];

      // Act
      const result = service.addScheduleAppointmentReminder(arrCurrent);

      // Assert
      expect(result.length).toBe(1);
    });

    it('should not add reminder if injection is not completed', () => {
      // Arrange
      const arrCurrent: Array<PreProcessCurrentDto> = [
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 1,
          status: 2, // injected
          date: '2023-01-01T00:00:00.000Z',
          regimen: {
            details: [
              { order: 1 },
              {
                order: 2,
                repeatFrequency: true,
                distanceType: 0,
                distanceValueByType: 30,
              },
            ],
          },
        } as unknown as PreProcessCurrentDto,
      ];

      // Mock checkRuleCompletedInjection to return false
      jest.spyOn(service, 'checkRuleCompletedInjection').mockReturnValue(false);

      // Act
      const result = service.addScheduleAppointmentReminder(arrCurrent);

      // Assert
      expect(result.length).toBe(1);
    });

    it('should not add reminder if there are existing scheduled appointments', () => {
      // Arrange
      const arrCurrent: Array<PreProcessCurrentDto> = [
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 1,
          status: 2, // injected
          date: '2023-01-01T00:00:00.000Z',
          regimen: {
            details: [
              { order: 1 },
              {
                order: 2,
                repeatFrequency: true,
                distanceType: 0,
                distanceValueByType: 30,
              },
            ],
          },
        } as unknown as PreProcessCurrentDto,
        {
          diseaseGroupId: 1,
          lcvId: 1,
          sku: 'SKU1',
          orderInjections: 3,
          status: 0, // scheduled
          vaccinatedNow: false,
          date: '2023-03-01T00:00:00.000Z',
          regimen: {
            details: [
              { order: 1 },
              {
                order: 2,
                repeatFrequency: true,
                distanceType: 0,
                distanceValueByType: 30,
              },
            ],
          },
        } as unknown as PreProcessCurrentDto,
      ];

      // Mock checkRuleCompletedInjection to return true
      jest.spyOn(service, 'checkRuleCompletedInjection').mockReturnValue(true);

      // Act
      const result = service.addScheduleAppointmentReminder(arrCurrent);

      // Assert
      expect(result.length).toBe(2); // No new items added
    });
  });
});
