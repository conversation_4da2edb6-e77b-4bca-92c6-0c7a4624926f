import { HttpException } from '@nestjs/common';
import { AxiosResponse } from '@nestjs/terminus/dist/health-indicator/http/axios.interfaces';
import { ErrorCode } from '../errors/error-code';
import { IErrorCoreResponse } from '../interfaces';

export class CoreException extends HttpException {
  constructor(exceptionResponse: IErrorCoreResponse, statusCode: number, config: AxiosResponse['config']) {
    super(
      HttpException.createBody({
        code: exceptionResponse?.error?.code || ErrorCode.CORE_INTERNAL_SERVER,
        message: exceptionResponse?.error?.message || ErrorCode.getError(ErrorCode.CORE_INTERNAL_SERVER),
        details: exceptionResponse?.error?.details || ErrorCode.getError(ErrorCode.CORE_INTERNAL_SERVER),
        validationErrors: exceptionResponse?.error?.validationErrors || null,
        config: config,
      }),
      statusCode,
    );
  }
}
