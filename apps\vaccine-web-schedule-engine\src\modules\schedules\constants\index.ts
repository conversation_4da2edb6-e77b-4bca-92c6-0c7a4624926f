/**
 * @TODO Định nghĩa rule type
 */
export enum RegimenRulesType {
  DEN_SOM = 1,
  THIEU_TUOI = 2,
  QUA_TUOI = 3,
}

/**
 * @TODO Định nghĩa ngày/tuần/tháng
 */
export enum TypeUnitFrom {
  DAYS = 0,
  WEEK = 1,
  MONTHS = 2,
  AGES = 3,
}

export const UnitFromName = {
  [TypeUnitFrom.DAYS]: 'ngày',
  [TypeUnitFrom.WEEK]: 'tuần',
  [TypeUnitFrom.MONTHS]: 'tháng',
  [TypeUnitFrom.AGES]: 'tuổi',
};

export const UnitFromCode = {
  [TypeUnitFrom.DAYS]: 'days',
  [TypeUnitFrom.WEEK]: 'weeks',
  [TypeUnitFrom.MONTHS]: 'months',
  [TypeUnitFrom.AGES]: 'years',
};

// define message cho rule tuổi
export const TEXT_RULE_NOT_OLD_AGE_WARING =
  'Mũi {order} của {vaccineName} sớm hơn {day} {unit} so với tuổi tối thiểu. Vui lòng kiểm tra lại trước khi xác nhận.';

export const TEXT_RULE_NOT_OLD_AGE_DANGER =
  'Mũi {order} của {vaccineName} sớm hơn {day} {unit} so với tuổi tối thiểu. Yêu cầu hội chẩn với trường hợp tiêm sớm hơn so với tuổi tối thiểu.';

export const TEXT_RULE_OVER_AGE_WARING =
  'Mũi {order} của {vaccineName} trễ hơn {day} {unit} so với tuổi tối đa. Vui lòng kiểm tra lại trước khi xác nhận.';

export const TEXT_RULE_OVER_AGE_DANGER =
  'Mũi {order} của {vaccineName} trễ hơn {day} {unit} so với tuổi tối đa. Yêu cầu hội chẩn với trường hợp tiêm trễ hơn so với tuổi tối đa.';

export enum TableName {
  VALIDATE = 'validate',
  SUGGEST = 'suggest',
  CHECK = 'check',
}
/**
 * @TODO
 *  1. Tìm diseaseGroup từ current với arrDiseaseGroupHard
 *  2. Hard code đối với phác đồ
 *     - 5 TRONG 1 (Bạch hầu, Ho gà, Uốn ván, Hib, Viêm gan B): ed5d7036-6a09-42c1-ae0e-758b276ee6ab
 *     - 5 TRONG 1 (Bạch hầu, Ho gà, Uốn ván, Hib, Bại liệt): 08f7c9b3-45e4-4036-b622-8d0698e668b9
 *     - 6 TRONG 1: 4d2c96e8-eac5-4c5f-b672-dff6a8483a7f
 *  3. So sánh số lần đã tiêm + số lần mua với số lần tiêm tối đa của phác đồ
 */
export const arrDiseaseGroupHard = [
  'ed5d7036-6a09-42c1-ae0e-758b276ee6ab',
  '08f7c9b3-45e4-4036-b622-8d0698e668b9',
  '4d2c96e8-eac5-4c5f-b672-dff6a8483a7f',
];

export type ScreenType = 'INDICATION' | 'SCHEDULE' | 'SLIDEBAR' | 'ECOM_PAYCHECK';
