import { Module, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { WinstonModule } from 'nest-winston';
import { BadRequestExceptionFilter, HttpExceptionFilter } from './common/filters';
import { AuthGuard } from './common/guards';
import { LoggingInterceptor, TimeoutInterceptor, TransformInterceptor } from './common/interceptors';
import { envValidator } from './common/validations';
import { MongooseConfigService, WinstonLoggerConfigService } from './config';
import { HealthModule } from './modules/health/health.module';
import { InjectionScheduleVaccineModule } from './modules/injection-schedule-vaccine/injection-schedule-vaccine.module';
import { SchedulesModule } from './modules/schedules/schedules.module';
import { MongooseModule } from '@nestjs/mongoose';
import { AllExceptionsFilter } from './common/filters/all-exception.filter';

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: `${process.cwd()}/apps/vaccine-schedule-engine-v2/.env`,
      isGlobal: true,
      validate: envValidator,
    }),
    WinstonModule.forRootAsync({
      useClass: WinstonLoggerConfigService,
    }),
    // TypeOrmModule.forRootAsync({
    //   useClass: DatabaseConfigService,
    // }),
    // S3Module.forRootAsync({
    //   useClass: S3ConfigService,
    // }),
    // RedisModule.forRootAsync({
    //   useClass: RedisConfigService,
    // }),
    MongooseModule.forRootAsync({
      useClass: MongooseConfigService,
    }),
    HealthModule,
    // InjectionSchedulesModule,
    InjectionScheduleVaccineModule,
    SchedulesModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        whitelist: false,
        skipMissingProperties: false,
        transform: true,
      }),
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: BadRequestExceptionFilter,
    },
  ],
})
export class AppModule {}
