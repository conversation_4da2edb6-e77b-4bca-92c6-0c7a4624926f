{"name": "schedule-engine-v2", "version": "1.0.0", "description": "schedule-engine-v2", "author": "quyen<PERSON>", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:consumer:dev": "PORT=3001 nest start --config=consumer.nest-cli.json --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "build:vaccine-schedule-engine-v2": "rimraf dist && nest build vaccine-schedule-engine-v2 && tsc --build apps/vaccine-schedule-engine-v2/tsconfig.app.build.json", "build:vaccine-web-schedule-engine": "rimraf dist && nest build vaccine-web-schedule-engine && tsc --build apps/vaccine-web-schedule-engine/tsconfig.app.build.json", "start:vaccine-schedule-engine-v2": "nest start vaccine-schedule-engine-v2 --debug --watch", "start:vaccine-web-schedule-engine": "nest start vaccine-web-schedule-engine --debug --watch", "debug:vaccine-schedule-engine-v2": "nest start vaccine-schedule-engine-v2 --debug --watch", "debug:vaccine-web-schedule-engine": "nest start vaccine-web-schedule-engine --debug --watch", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- migration:generate -d src/config/migration.config.ts ./src/migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./src/migrations/$npm_config_name", "migration:up": "npm run typeorm -- migration:run -d dist/config/migration.config.js", "migration:down": "npm run typeorm -- migration:revert -d dist/config/migration.config.js", "schema:drop": "npm run typeorm -- schema:drop -d dist/config/migration.config.js", "schema:sync": "npm run typeorm -- schema:sync -d dist/config/migration.config.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "is-ci || husky install"}, "dependencies": {"@liaoliaots/nestjs-redis": "^9.0.5", "@liaoliaots/nestjs-redis-health": "^9.0.0", "@nestjs/axios": "^1.0.1", "@nestjs/common": "^9.0.5", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.5", "@nestjs/microservices": "^9.0.8", "@nestjs/mongoose": "^10.1.0", "@nestjs/platform-express": "^9.0.5", "@nestjs/swagger": "^6.0.4", "@nestjs/terminus": "^9.0.0", "@nestjs/typeorm": "^9.0.1", "@opentelemetry/auto-instrumentations-node": "^0.31.2", "@opentelemetry/exporter-trace-otlp-http": "^0.31.0", "@opentelemetry/propagator-b3": "^1.5.0", "@opentelemetry/resources": "^1.5.0", "@opentelemetry/sdk-node": "^0.31.0", "@opentelemetry/semantic-conventions": "^1.5.0", "amqp-connection-manager": "^4.1.5", "amqplib": "^0.10.2", "aws-sdk": "^2.1231.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "compression": "^1.7.4", "elastic-apm-node": "^3.37.0", "helmet": "^5.1.1", "ict-nest-s3": "^1.1.18", "ioredis": "^5.3.2", "jsonpath-plus": "^7.2.0", "jwt-decode": "^3.1.2", "lc-nest-http-client": "^1.1.6", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^8.8.2", "nest-winston": "^1.7.0", "pg": "^8.8.0", "qs": "^6.11.0", "randomstring": "^1.3.0", "reflect-metadata": "^0.1.13", "remove-accents": "^0.5.0", "request-ip": "^3.3.0", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "tsconfig-paths": "4.0.0", "typeorm": "^0.3.11", "uuid": "^9.0.0", "vac-nest-cds-engine": "^1.1.7", "vac-nest-examination": "^3.3.4", "vac-nest-family": "^1.2.23", "vac-nest-history": "^1.4.0", "vac-nest-ims": "^1.0.16", "vac-nest-ims-booking": "^1.0.11", "vac-nest-order-injection": "^1.0.2", "vac-nest-order-rule-engine": "^1.2.1", "vac-nest-osr": "^3.1.2", "vac-nest-pim-app": "^1.3.0", "vac-nest-regimen": "^2.4.0", "vac-nest-schedule": "^2.5.2", "winston": "^3.8.1", "winston-elasticsearch": "^0.17.1"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.1", "@nestjs/testing": "^9.0.5", "@types/express": "^4.17.13", "@types/jest": "28.1.6", "@types/multer": "^1.4.7", "@types/node": "^18.6.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.31.0", "dotenv": "^16.0.1", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "is-ci": "^3.0.1", "jest": "28.1.3", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "run-script-webpack-plugin": "^0.1.1", "source-map-support": "^0.5.21", "supertest": "^6.2.4", "ts-jest": "28.0.7", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "typescript": "^4.3.5", "webpack": "^5.73.0", "webpack-node-externals": "^3.0.0"}, "lint-staged": {"*.{ts,js}": ["npm run lint", "npm run format", "npm run test"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["modules/**/*.(t|j)s"], "coverageThreshold": {"global": {"lines": 90}}, "detectOpenHandles": true, "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}