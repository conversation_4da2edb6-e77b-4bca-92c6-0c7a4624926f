import { SkuLimitRuleService } from './sku-limit-rule.service';
import { PreProcessCurrentDto, PreProcessFutureDto } from '../dto/pre-process-data.dto';
import { ExtraDto } from '../dto/extra.dto';
import { RuleRes } from '../dto/common';
import moment from 'moment';
import { DEFAULT_DATE_SKU_LIMIT, FORMAT_DATE } from 'apps/vaccine-schedule-engine-v2/src/constants';

describe('SkuLimitRuleService', () => {
  let service: SkuLimitRuleService;

  beforeEach(() => {
    service = new SkuLimitRuleService();
  });

  describe('isPackage', () => {
    it('should return false if no other SKUs in cart and timesBuy is 1', () => {
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const extra: ExtraDto = {
        osrLimitedQuota: [],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [],
        arrDiseaseGroupIdDefined: [],
      };

      expect(service.isPackage(current, arrFuture, extra)).toBe(false);
    });

    it('should return true if no other SKUs in cart and timesBuy is 2', () => {
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        screenType: 'INDICATION',
        sku: '',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const extra: ExtraDto = { osrLimitedQuota: [], timesBuy: 2, listDiseaseGroupIdInCart: [] };

      expect(service.isPackage(current, arrFuture, extra)).toBe(true);
    });

    it('should return true if there are more than 2 unique dates in the future SKUs', () => {
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().add(3, 'days').format(),
        status: 0,
        lcvId: '',
        priority: 0,
        screenType: 'INDICATION',
        sku: '',
      };
      const arrFuture: PreProcessFutureDto[] = [
        {
          diseaseGroupId: '2',
          date: new Date(),
          status: 0,
          sku: '',
        },
        {
          diseaseGroupId: '3',
          date: new Date(),
          status: 0,
          sku: '',
        },
      ];
      const extra: ExtraDto = {
        osrLimitedQuota: [],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [
          { diseaseGroupId: '2', quantity: 1 },
          { diseaseGroupId: '3', quantity: 1 },
        ],
      };

      service.isPackage(current, arrFuture, extra);

      expect(service.isPackage(current, arrFuture, extra)).toBe(true);
    });

    it('should return false if there is only 1 unique date in the future SKUs', () => {
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [
        {
          diseaseGroupId: '2',
          date: new Date(),
          status: 0,
          sku: '',
        },
      ];
      const extra: ExtraDto = {
        osrLimitedQuota: [],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [
          {
            diseaseGroupId: '2',
            quantity: 1,
          },
        ],
      };

      expect(service.isPackage(current, arrFuture, extra)).toBe(false);
    });

    it('should return true if there is 2 sku and sl 2', () => {
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [
        {
          diseaseGroupId: '2',
          date: new Date(),
          status: 0,
          sku: '',
        },
        {
          diseaseGroupId: '2',
          date: moment().add(1, 'days').toDate(),
          status: 0,
          sku: '',
        },
      ];
      const extra: ExtraDto = {
        osrLimitedQuota: [],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [
          {
            diseaseGroupId: '2',
            quantity: 2,
          },
        ],
      };

      expect(service.isPackage(current, arrFuture, extra)).toBe(true);
    });

    it('should return false if there is 2 sku and sl 1 cùng ngày', () => {
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [
        {
          diseaseGroupId: '2',
          date: new Date(),
          status: 0,
          sku: '',
        },
        {
          diseaseGroupId: '2',
          date: moment().add(1, 'days').toDate(),
          status: 0,
          sku: '',
        },
      ];
      const extra: ExtraDto = {
        osrLimitedQuota: [],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [
          {
            diseaseGroupId: '2',
            quantity: 1,
          },
        ],
      };

      expect(service.isPackage(current, arrFuture, extra)).toBe(false);
    });

    it('should return true if there is 2 sku and sl 1 != date', () => {
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [
        {
          diseaseGroupId: '2',
          date: moment().add(1, 'days').toDate(),
          status: 0,
          sku: '',
        },
        {
          diseaseGroupId: '2',
          date: moment().add(3, 'days').toDate(),
          status: 0,
          sku: '',
        },
      ];
      const extra: ExtraDto = {
        osrLimitedQuota: [],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [
          {
            diseaseGroupId: '2',
            quantity: 1,
          },
        ],
      };

      expect(service.isPackage(current, arrFuture, extra)).toBe(true);
    });
  });

  describe('checkRuleSkuLimitForSuggest', () => {
    it('should return the same date if current status is not 0 or diseaseGroupId is not in osrLimitedQuota', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 1,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const extra: ExtraDto = { osrLimitedQuota: [{ diseaseGroupId: '2' }], timesBuy: 1, listDiseaseGroupIdInCart: [] };

      expect(service.checkRuleSkuLimitForSuggest(date, current, arrFuture, extra)).toEqual(date);
    });

    it('should return a new date if the date difference is less than DEFAULT_DATE_SKU_LIMIT', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const extra: ExtraDto = {
        osrLimitedQuota: [{ diseaseGroupId: '1' }],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [],
        arrDiseaseGroupIdDefined: ['1'],
      };

      const result = service.checkRuleSkuLimitForSuggest(date, current, arrFuture, extra);
      const expectedDate = moment().add(DEFAULT_DATE_SKU_LIMIT, 'days').toDate();

      expect(moment(result).format(FORMAT_DATE)).toEqual(moment(expectedDate).format(FORMAT_DATE));
    });

    it('should return the same date if there is no quota information', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const extra: ExtraDto = { osrLimitedQuota: [{ diseaseGroupId: '1' }], timesBuy: 2, listDiseaseGroupIdInCart: [] };

      expect(moment(service.checkRuleSkuLimitForSuggest(date, current, arrFuture, extra)).format(FORMAT_DATE)).toEqual(
        moment(date).format(FORMAT_DATE),
      );
    });
  });

  describe('checkRuleSkuLimitForValidate', () => {
    it('should return false when screenType !== indication', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'SCHEDULE',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const rules: RuleRes[] = [];
      const extra: ExtraDto = { osrLimitedQuota: [{ diseaseGroupId: '1' }], timesBuy: 1, listDiseaseGroupIdInCart: [] };

      expect(service.checkRuleSkuLimitForValidate(date, current, arrFuture, rules, extra)).toEqual(false);
    });

    it('should return false when screenType !== indication', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'SLIDEBAR',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const rules: RuleRes[] = [];
      const extra: ExtraDto = { osrLimitedQuota: [{ diseaseGroupId: '1' }], timesBuy: 1, listDiseaseGroupIdInCart: [] };

      expect(service.checkRuleSkuLimitForValidate(date, current, arrFuture, rules, extra)).toEqual(false);
    });
    it('should return false if current status is not 0 or diseaseGroupId is not in osrLimitedQuota', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 1,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const rules: RuleRes[] = [];
      const extra: ExtraDto = { osrLimitedQuota: [{ diseaseGroupId: '2' }], timesBuy: 1, listDiseaseGroupIdInCart: [] };

      expect(service.checkRuleSkuLimitForValidate(date, current, arrFuture, rules, extra)).toBe(false);
    });

    it('should return the same date if the date difference is 0', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const rules: RuleRes[] = [];
      const extra: ExtraDto = {
        osrLimitedQuota: [{ diseaseGroupId: '1' }],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [],
        arrDiseaseGroupIdDefined: ['1'],
      };

      const result = service.checkRuleSkuLimitForValidate(date, current, arrFuture, rules, extra);
      expect(result).toEqual(false);
    });

    it('should return false if there is no quota information', () => {
      const date = new Date();
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const rules: RuleRes[] = [];
      const extra: ExtraDto = {
        osrLimitedQuota: [{ diseaseGroupId: '1' }],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [],
        arrDiseaseGroupIdDefined: ['1'],
      };

      expect(service.checkRuleSkuLimitForValidate(date, current, arrFuture, rules, extra)).toBe(false);
    });

    it('should add a rule if the date difference is less than DEFAULT_DATE_SKU_LIMIT', () => {
      const date = new Date(moment().add(1, 'days').format());
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().add(1, 'days').format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const rules: RuleRes[] = [];
      const extra: ExtraDto = {
        osrLimitedQuota: [{ diseaseGroupId: '1' }],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [],
        arrDiseaseGroupIdDefined: ['1'],
      };

      service.checkRuleSkuLimitForValidate(date, current, arrFuture, rules, extra);

      expect(rules.length).toBe(1);
      expect(rules[0].type).toBe('DANGER');
    });

    it('should add a rule if the quota is 0', () => {
      const date = new Date(moment().add(10, 'days').format());
      const current: PreProcessCurrentDto = {
        diseaseGroupId: '1',
        date: moment().add(10, 'days').format(),
        status: 0,
        lcvId: '',
        priority: 0,
        sku: '',
        screenType: 'INDICATION',
      };
      const arrFuture: PreProcessFutureDto[] = [];
      const rules: RuleRes[] = [];
      const extra: ExtraDto = {
        osrLimitedQuota: [{ diseaseGroupId: '1', appointmentDate: moment().add(10, 'days').format(), quota: 0 }],
        timesBuy: 1,
        listDiseaseGroupIdInCart: [],
        arrDiseaseGroupIdDefined: ['1'],
      };

      service.checkRuleSkuLimitForValidate(date, current, arrFuture, rules, extra);

      expect(rules.length).toBe(1);
      expect(rules[0].type).toBe('DANGER');
    });
  });
});
