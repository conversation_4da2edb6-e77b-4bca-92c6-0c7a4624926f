import { Injectable, Logger } from '@nestjs/common';
import { RuleRes } from '../dto/common';
import { PreProcessCurrentDto, PreProcessFutureDto } from '../dto/pre-process-data.dto';
import { ExtraDto } from '../dto/extra.dto';
import _ from 'lodash';
import moment from 'moment';
import { EnumDistanceType, EnumDistanceTypeString, FORMAT_DATE, RuleType } from '../../../constants';

@Injectable()
export class VaccineConversionRuleService {
  private logger = new Logger(VaccineConversionRuleService.name);

  /**
   * Check for vaccine conversion rules during suggestion
   * @param date Suggested date for current vaccine
   * @param current Current vaccine to be administered
   * @param arrFuture Array of future and past vaccinations
   * @param extra Extra data including patient info
   * @param rules Output array for rule validation results
   * @returns Boolean indicating if any conversion rule was applied
   */
  checkVaccineConversionRule(
    date: Date,
    current: PreProcessCurrentDto,
    arrFuture: PreProcessFutureDto[],
    extra: ExtraDto = {},
    rules: RuleRes[],
  ): boolean {
    const { sku } = current;
    extra.isRuleConversion = false;

    this.logger.log(`[CONVERSION RULE] Checking conversion rules for ${sku}`);

    // Get conversion rules for current SKU
    const conversionRules = extra?.vaccineConversion?.[sku];
    if (!conversionRules?.length) {
      return false;
    }

    // Get all potential target SKUs mentioned in conversion rules
    const targetSkus = conversionRules.map((rule) => rule.Sku2);

    // Find future vaccines that match target SKUs in conversion rules
    const matchingFutureVaccines = arrFuture.filter((vaccine) => targetSkus.includes(vaccine.sku));
    if (!matchingFutureVaccines.length) {
      return false;
    }

    // Count current SKU instances in future vaccines
    const currentSkuVaccines = arrFuture.filter((vaccine) => vaccine.sku === sku);
    const currentSkuCount = currentSkuVaccines.length;

    // Find most recent future vaccine among matches
    const mostRecentVaccine = _.maxBy(matchingFutureVaccines, 'date');
    if (!mostRecentVaccine) {
      return false;
    }

    // Calculate future vaccine count for the most recent matching SKU
    let futureVaccineCount = arrFuture.filter((vaccine) => vaccine.sku === mostRecentVaccine.sku).length;

    // Cap the count to maximum defined in rules
    const maxRuleCount = Math.max(...conversionRules?.map((e) => e.OrderInjection2));
    futureVaccineCount = Math.min(futureVaccineCount, maxRuleCount);

    // Find applicable rule based on exact matching criteria
    const applicableRule = conversionRules.find(
      (rule) =>
        rule.Sku2 === mostRecentVaccine.sku &&
        rule.OrderInjection2 === futureVaccineCount &&
        rule.Sku1 === sku &&
        rule.OrderInjection1 === currentSkuCount + 1,
    );

    if (!applicableRule) {
      return false;
    }

    // Determine time unit for comparison
    const timeUnit: moment.unitOfTime.Diff = this.getTimeUnitForDistanceType(applicableRule.MinDistanceType);

    // Calculate time difference between dates
    const timeDifference = moment(moment(date).format(FORMAT_DATE)).diff(
      moment(moment(mostRecentVaccine?.date).format(FORMAT_DATE)),
      timeUnit,
    );
    extra.isRuleConversion = true;
    extra.dateConversionLatest = mostRecentVaccine?.date;

    // Apply rule if minimum distance is not met
    if (timeDifference < applicableRule.MinDistanceValue) {
      this.logger.log(`[CONVERSION RULE] Conversion rule applied for ${sku} to ${mostRecentVaccine?.sku}`);

      // Add rule violation to rules array
      rules.push({
        text: `Khoảng cách giữa ${current?.regimen?.vaccine?.name} và mũi ${
          mostRecentVaccine?.regimen?.vaccine?.name
        } chưa đủ ${applicableRule?.MinDistanceValue} ${
          EnumDistanceTypeString[applicableRule?.MinDistanceType]
        }. Vui lòng chọn ngày khác hoặc thực hiện hội chẩn`,
        type: applicableRule.WarningType === 1 ? 'WARNING' : 'DANGER',
        value: applicableRule?.MinDistanceValue,
        ruleType: RuleType.ChuyenDoiVacXin,
        ruleName: 'Chuyển đổi vắc xin',
        isAllowEvaluate: true,
        ageUnitCode: applicableRule.MinDistanceType,
      });
      return true;
    }

    return false;
  }

  /**
   * Helper method to convert distance type to moment time unit
   */
  private getTimeUnitForDistanceType(distanceType: EnumDistanceType): moment.unitOfTime.Diff {
    switch (distanceType) {
      case EnumDistanceType.days:
        return 'days';
      case EnumDistanceType.months:
        return 'months';
      case EnumDistanceType.years:
        return 'years';
      default:
        return 'days';
    }
  }
}
