import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Request } from 'express';
import _ from 'lodash';
import moment from 'moment';
import { ExaminationCoreService, GetEvaluationRequestByLcvIdRes } from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import { VacHistoryService } from 'vac-nest-history';
import { IMSService } from 'vac-nest-ims';
import { IMSBookingService } from 'vac-nest-ims-booking';
import { OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { OsrService } from 'vac-nest-osr';
import { PIMAppService } from 'vac-nest-pim-app';
import { RegimenService } from 'vac-nest-regimen';
import { ScheduleCoreService } from 'vac-nest-schedule';
import { SchedulesAdapter } from '../adapters/schedules.abstract';
import { ScheduleRulesService } from './schedule-rules.service';
import { RuleMaxInjectionService } from './maxInjection-rule.service';
import { AgeRuleService } from './age-rule.service';
import { SkuLimitRuleService } from './sku-limit-rule.service';
import {
  CheckAllRuleSchedulesDto,
  PreProcessCurrentDto,
  PreProcessFutureDto,
  RuleRes,
  SuggestScheduleDto,
  SuggestScheduleRes,
} from '../dto';
import { diffDate } from '@app/utils/utilities/function-common';
import { SystemException } from '@app/utils/common/exceptions';
import { ErrorCode } from '@app/utils/common/errors/error-code';

@Injectable()
export class SchedulesWebService extends SchedulesAdapter {
  shopCode: string;

  constructor(
    protected readonly familyService: FamilyService,
    protected readonly imsService: IMSService,
    protected readonly regimenService: RegimenService,
    protected readonly pimAppService: PIMAppService,
    protected readonly vacHistoryService: VacHistoryService,
    protected readonly scheduleRulesService: ScheduleRulesService,
    protected readonly examinationCoreService: ExaminationCoreService,
    protected readonly imsBookingService: IMSBookingService,
    protected readonly ruleMaxInjectionService: RuleMaxInjectionService,
    protected readonly ageRuleService: AgeRuleService,
    protected readonly skuLimitRuleService: SkuLimitRuleService,
    protected readonly osrService: OsrService,
    protected readonly scheduleCoreService: ScheduleCoreService,
    protected readonly orderRuleEngineService: OrderRuleEngineService,
    @Inject('REQUEST')
    private readonly request: Request,
  ) {
    super(
      familyService,
      imsService,
      regimenService,
      pimAppService,
      vacHistoryService,
      imsBookingService,
      osrService,
      scheduleCoreService,
      orderRuleEngineService,
    );
    this.shopCode = this.request.headers['shop-code'] as string;
  }

  private readonly BUSINESS_DAY_START_HOUR = +process.env.BUSINESS_DAY_START_HOUR || 8;
  private readonly BUSINESS_DAY_END_HOUR = +process.env.BUSINESS_DAY_END_HOUR || 16;

  async suggestSchedule(suggestScheduleDto: SuggestScheduleDto): Promise<any> {
    const preProcessData = await this.preProcessData(suggestScheduleDto, this.shopCode);
    /**
     * @TODO group by diseaseGroupId-lcvId
     */
    const groupedData = _.groupBy(
      preProcessData.arrCurrent,
      (entry: PreProcessCurrentDto) => `${entry.diseaseGroupId}-${entry.lcvId}`,
    );

    const { arrFuture } = preProcessData;
    _.forEach(groupedData, (arrCurrent: Array<PreProcessCurrentDto>) => {
      _.forEach(arrCurrent, (current: PreProcessCurrentDto) => {
        // bypass rule có ngày rồi k suggest nữa
        if (current?.date) {
          Logger.log(`current have Date: ${current.sku} - ${current.orderInjections} - ${current.date}`);
          // future có thông tin k cần push
          const arrFutureFind = arrFuture.find(
            (f) => f.sku === current.sku && f.orderInjections === current.orderInjections,
          );
          if (!arrFutureFind) {
            arrFuture.push(plainToInstance(PreProcessFutureDto, current));
          }
          return;
        }
        Logger.log(
          `================START SUGGEST DATE ${current.sku} - ${current.orderInjections} ===================`,
        );
        const date = this.getDateTheBest(current, arrFuture, suggestScheduleDto?.expectedDate);
        current.date = date;
        Logger.log(`DATE FINAL: ${date}`);
        Logger.log(`================END SUGGEST DATE ${current.sku} - ${current.orderInjections} ===================`);
        arrFuture.push(plainToInstance(PreProcessFutureDto, current));
      });
    });

    // sort by date
    preProcessData.arrFuture = _.sortBy(preProcessData.arrFuture, 'date');
    preProcessData.arrCurrent = this.groupVaccineBySku(preProcessData.arrCurrent, suggestScheduleDto?.expectedDate);
    preProcessData.arrCurrent = this.scheduleRulesService.addScheduleAppointmentReminder(preProcessData.arrCurrent);

    return plainToInstance(SuggestScheduleRes, preProcessData, {
      excludeExtraneousValues: true,
      exposeUnsetFields: false,
    });
  }

  async validateSchedule(suggestScheduleDto: SuggestScheduleDto): Promise<any> {
    const preProcessData = await this.preProcessData(suggestScheduleDto, this.shopCode);
    /**
     * @TODO group by diseaseGroupId-lcvId
     */
    const groupedData = _.groupBy(
      preProcessData.arrCurrent,
      (entry: PreProcessCurrentDto) => `${entry.diseaseGroupId}-${entry.lcvId}`,
    );

    const { arrFuture, extra } = preProcessData;
    _.forEach(groupedData, (arrCurrent: Array<PreProcessCurrentDto>) => {
      _.forEach(arrCurrent, (current: PreProcessCurrentDto) => {
        if (current?.date && current?.isChangeDate) {
          Logger.log(`Current change Date: ${current.sku} - ${current.orderInjections} - ${current.date}`);
          const rules: RuleRes[] = [];
          if (process.env.ENABLE_RULE_LIMIT_QUOTA === 'true') {
            this.skuLimitRuleService.checkRuleSkuLimitForValidate(
              new Date(current.date),
              current,
              arrFuture,
              rules,
              extra,
            );
          }
          this.scheduleRulesService.checkRuleInventory(new Date(current.date), current, rules, this.shopCode);
          this.scheduleRulesService.checkRuleConflict(new Date(current.date), current, arrFuture, rules);
          this.scheduleRulesService.checkRuleDistance(new Date(current.date), current, arrFuture, rules);
          this.scheduleRulesService.checkRuleAge(new Date(current.date), current, rules);
          this.ruleMaxInjectionService.checkRuleMaxInjectionDiseaseGroup(current, arrFuture, rules, arrCurrent);
          current.rules = rules;
          arrFuture.push(plainToInstance(PreProcessFutureDto, current));
          return;
        }

        if (current.date) {
          Logger.log(`current have Date: ${current.sku} - ${current.orderInjections} - ${current.date}`);
          // future có thông tin k cần push
          const arrFutureFind = arrFuture.find(
            (f) => f.sku === current.sku && f.orderInjections === current.orderInjections,
          );
          if (!arrFutureFind) {
            arrFuture.push(plainToInstance(PreProcessFutureDto, current));
          }
          return;
        }

        Logger.log(
          `================START SUGGEST DATE ${current.sku} - ${current.orderInjections} ===================`,
        );
        const date = this.getDateTheBest(current, arrFuture);
        current.date = date;
        Logger.log(`DATE FINAL: ${date}`);
        Logger.log(`================END SUGGEST DATE ${current.sku} - ${current.orderInjections} ===================`);
        arrFuture.push(plainToInstance(PreProcessFutureDto, current));
      });
    });

    preProcessData.arrFuture = _.sortBy(preProcessData.arrFuture, 'date');
    preProcessData.arrCurrent = this.groupVaccineBySku(preProcessData.arrCurrent, suggestScheduleDto?.expectedDate);

    return plainToInstance(SuggestScheduleRes, preProcessData, {
      excludeExtraneousValues: true,
      exposeUnsetFields: false,
    });
  }

  /**
   * @description Lấy ngày tiêm tốt nhất
   */
  getDateTheBest(
    current: PreProcessCurrentDto,
    arrFuture: Array<PreProcessFutureDto>,
    expectedDate?: Date,
    // extra?: ExtraDto,
  ) {
    // nếu ngày dự sinh lớn hơn ngày hiện tại
    // gán expectedDate bằng ngày dự sinh
    if (current?.person?.dateOfBirth) {
      const diffDates = diffDate(new Date(), new Date(current?.person?.dateOfBirth), 'days');
      if (diffDates > 0) expectedDate = current?.person?.dateOfBirth;
    }
    let dateTheBest = expectedDate ? new Date(expectedDate) : new Date();

    Logger.log(`Start Suggest: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);
    // Không phải mũi 1 => Lấy mũi trước đó + bao nhiêu ngày
    if (current.orderInjections !== 1) {
      const { distanceType, distanceValueByType } = this.scheduleRulesService.getDistanceByOrderInjection(current);
      Logger.log(`distanceType: ${distanceType}`);
      Logger.log(`distanceValueByType: ${distanceValueByType}`);
      // Lấy ngây tiêm trước đó
      const preOrderInjection: PreProcessFutureDto = _.maxBy(
        arrFuture.filter((entry: PreProcessFutureDto) => entry.diseaseGroupId === current.diseaseGroupId),
        'orderInjections',
      );
      Logger.log(
        `preOrderInjection: ${preOrderInjection?.sku} - ${preOrderInjection?.orderInjections} - ${preOrderInjection?.date}`,
      );
      if (preOrderInjection) {
        dateTheBest = this.scheduleRulesService.addDateInDefined(
          new Date(preOrderInjection?.date),
          distanceType,
          distanceValueByType,
        );
      }
      Logger.log(
        `Suggest orderInjection !== 1: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`,
      );
    }

    // case update ngày hẹn
    if (expectedDate && new Date(expectedDate) > new Date(dateTheBest)) {
      dateTheBest = new Date(expectedDate);
    }

    /**
     * @TODO check rule tuổi
     */
    let rules: Array<RuleRes> = [];
    const ruleAge = this.scheduleRulesService.checkRuleAge(new Date(dateTheBest), current, rules);
    if (ruleAge && rules?.at(0)?.value > 0) {
      const newDate = this.ageRuleService.addDateInAge(
        new Date(current.person.dateOfBirth),
        rules?.at(0)?.value,
        rules?.at(0)?.ageUnitCode,
        rules?.at(0)?.equalAge,
      );
      dateTheBest = newDate;
      Logger.log(`Suggest rule Age: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);
    }

    /**
     * @TODO Check rule tồn
     */
    // rules = [];
    // const ruleInventory = this.scheduleRulesService.checkRuleInventory(
    //   new Date(dateTheBest),
    //   current,
    //   rules,
    //   this.shopCode,
    // );
    // if (ruleInventory) {
    //   dateTheBest = new Date(moment(dateTheBest).add(rules?.at(0)?.value, 'days').format());
    //   Logger.log(`Suggest rule Inventory: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);
    // }

    /**
     * @TODO xử lý ngày trong khoảng nghĩ lễ
     */
    dateTheBest = this.scheduleRulesService.checkSuggestCRHoliday(dateTheBest);

    // Ngày hiện tại > hơn ngày Tốt nhất thì => ngày hiện tại.
    // vì có thể mũi tiêm trước đó quá lâu. 1 năm trước + khoản cách thì lúc đó vẫn là ngày quá khứ
    if (new Date() > new Date(dateTheBest)) {
      dateTheBest = new Date();
      Logger.log(`Suggest Date is past: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);
    }

    /**
     * @TODO xử lý thao tác ngày sau 16h chiều cho web
     */
    dateTheBest = this.scheduleRulesService.ruleAfterFourAfternoonForWeb(dateTheBest);

    // if (process.env.ENABLE_RULE_LIMIT_QUOTA === 'true') {
    //   dateTheBest = this.skuLimitRuleService.checkRuleSkuLimitForSuggest(dateTheBest, current, arrFuture, extra);
    // }

    /**
     * @TODO lặp 700 lần để kiếm ngày phù hợp nhất
     */
    let index = 700;
    while (index) {
      index--;
      if (index === 0) break;

      rules = [];
      /**
       * @TODO Check rule 2 tiêm 1 uống/1 ngày
       */
      const ruleConflict = this.scheduleRulesService.checkRuleConflict(
        new Date(dateTheBest),
        current,
        arrFuture,
        rules,
      );

      if (ruleConflict) {
        dateTheBest = new Date(moment(dateTheBest).add(rules?.at(0)?.value, 'days').format());
        Logger.log(`Suggest rule conflict: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);
        continue;
      }

      /**
       * @TODO Check rule khoản kách giữa các lần tiêm
       */
      const ruleDistance = this.scheduleRulesService.checkRuleDistance(
        new Date(dateTheBest),
        current,
        arrFuture,
        rules,
      );
      if (ruleDistance) {
        dateTheBest = new Date(moment(dateTheBest).add(rules?.at(0)?.value, 'days').format());
        Logger.log(
          `Suggest rule khoản cách: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`,
        );
        continue;
      }

      break;
    }

    Logger.log(
      `DATE THE BEST: ${current.sku} - injections: ${current.orderInjections} - bestDate: ${dateTheBest.toISOString()}`,
    );

    return new Date(dateTheBest).toISOString();
  }

  /**
   * @description check all rule nhắc hẹn
   */
  async checkAllSchedules(checkScheduleDto: CheckAllRuleSchedulesDto) {
    if (!checkScheduleDto?.arrCurrent?.length) {
      return {
        arrCurrent: [],
        arrFuture: [],
      };
    }
    // Check rule trước khi validate
    const evaluationRequest = await this.examinationCoreService.getEvaluationRequestByLcvId(
      checkScheduleDto?.arrCurrent?.at(0).lcvId,
    );
    // sort by date
    const evaluationRequestSortDate: Array<GetEvaluationRequestByLcvIdRes> = _.orderBy(
      evaluationRequest,
      ['createdDate'],
      ['desc'],
    );
    if (evaluationRequestSortDate?.at(0)?.status === 0) {
      throw new SystemException(
        {
          code: ErrorCode.HCYK_OPEN,
          message: ErrorCode.getError(ErrorCode.HCYK_OPEN),
          details: ErrorCode.getError(ErrorCode.HCYK_OPEN),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    const preProcessData = await this.preProcessData(checkScheduleDto, this.shopCode);

    /**
     * @TODO chặn Gardasil 4 cho nam khi mua mũi 1
     * 00038235 sku của Gardasil 4
     */
    const findGardasil4 = preProcessData?.arrCurrent?.filter((i) => i?.sku === '00038235' && i?.status === 0);
    // nếu có findGardasil4 và giới tính là nam thì check tiếp mũi thứ 1
    if (findGardasil4 && findGardasil4?.at(0)?.person?.gender === 0) {
      const min = _.minBy(findGardasil4, 'orderInjections');
      if (min?.orderInjections === 1) {
        throw new SystemException(
          {
            code: ErrorCode.BLOCK_GARDASIL_4_FOR_NAM,
            message: ErrorCode.getError(ErrorCode.BLOCK_GARDASIL_4_FOR_NAM),
            details: ErrorCode.getError(ErrorCode.BLOCK_GARDASIL_4_FOR_NAM),
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }
    /**
     * @TODO group by diseaseGroupId-lcvId
     */
    const groupedData = _.groupBy(
      preProcessData.arrCurrent,
      (entry: PreProcessCurrentDto) => `${entry.diseaseGroupId}-${entry.lcvId}`,
    );

    const { arrFuture } = preProcessData;
    arrFuture.push(
      ...preProcessData?.arrCurrent?.filter((e) => e.status !== 2).map((e) => plainToInstance(PreProcessFutureDto, e)),
    );
    _.forEach(groupedData, (arrCurrent: Array<PreProcessCurrentDto>) => {
      // sort by mui thu
      arrCurrent = _.sortBy(arrCurrent, 'orderInjections');
      const arrFutureFiler = arrFuture.filter(
        (e) =>
          e.diseaseGroupId !== arrCurrent[0].diseaseGroupId ||
          (e.diseaseGroupId === arrCurrent[0].diseaseGroupId && e.status === 2),
      );
      _.forEach(arrCurrent, (current: PreProcessCurrentDto) => {
        if (current?.date && current?.status !== 2) {
          // status === 2 đã tiêm
          Logger.log(`Current change Date: ${current.sku} - ${current.orderInjections} - ${current.date}`);
          const rules: RuleRes[] = [];
          this.scheduleRulesService.checkRuleInventory(
            new Date(current.date),
            current,
            rules,
            this.shopCode,
            true,
            checkScheduleDto?.screenType,
          );
          if (checkScheduleDto?.screenType !== 'ECOM_PAYCHECK') {
            this.scheduleRulesService.checkRuleConflict(new Date(current.date), current, arrFutureFiler, rules);
            this.scheduleRulesService.checkRuleDistance(new Date(current.date), current, arrFutureFiler, rules);
            this.scheduleRulesService.checkRuleAge(new Date(current.date), current, rules);
            this.ruleMaxInjectionService.checkRuleMaxInjectionDiseaseGroup(current, arrFuture, rules, arrCurrent);
          }
          current.rules = rules;
          arrFutureFiler.push(plainToInstance(PreProcessFutureDto, current));
          return;
        }
      });
    });

    preProcessData.arrFuture = _.sortBy(preProcessData.arrFuture, 'date');
    preProcessData.arrCurrent = this.scheduleRulesService.addRuleNeedReview(
      preProcessData.arrCurrent,
      evaluationRequest,
    );
    preProcessData.arrCurrent = this.groupVaccineBySku(preProcessData.arrCurrent, checkScheduleDto?.expectedDate);

    return plainToInstance(SuggestScheduleRes, preProcessData, {
      excludeExtraneousValues: true,
      exposeUnsetFields: false,
    });
  }

  /**
   * @description Lấy ngày tiêm tốt nhất
   */
  getDateTheBestForPricing(current: PreProcessCurrentDto, arrFuture: Array<PreProcessFutureDto>, expectedDate?: Date) {
    let dateTheBest = expectedDate ? new Date(expectedDate) : new Date();
    Logger.log(`Start Suggest: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);
    let rules: Array<RuleRes> = [];
    /**
     * @TODO lặp 700 lần để kiếm ngày phù hợp nhất
     */
    let index = 700;
    while (index) {
      index--;
      if (index === 0) break;

      rules = [];
      /**
       * @TODO Check rule 2 tiêm 1 uống/1 ngày
       */
      const ruleConflict = this.scheduleRulesService.checkRuleConflict(
        new Date(dateTheBest),
        current,
        arrFuture,
        rules,
      );

      if (ruleConflict) {
        dateTheBest = new Date(moment(dateTheBest).add(rules?.at(0)?.value, 'days').format());
        Logger.log(`Suggest rule conflict: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);
        continue;
      }

      break;
    }

    Logger.log(`DATE THE BEST FOR PRICING: ${current.sku} - ${current.orderInjections} - ${dateTheBest.toISOString()}`);

    return new Date(dateTheBest).toISOString();
  }

  async suggestScheduleForPricing(suggestScheduleDto: SuggestScheduleDto): Promise<any> {
    const preProcessData = await this.preProcessDataForPricing(suggestScheduleDto, this.shopCode);
    /**
     * @TODO group by diseaseGroupId-lcvId
     */
    const groupedData = _.groupBy(
      preProcessData.arrCurrent,
      (entry: PreProcessCurrentDto) => `${entry.diseaseGroupId}-${entry.lcvId}`,
    );
    const { arrFuture } = preProcessData;
    _.forEach(groupedData, (arrCurrent: Array<PreProcessCurrentDto>) => {
      _.forEach(arrCurrent, (current: PreProcessCurrentDto) => {
        // bypass rule có ngày rồi k suggest nữa
        if (current?.date) {
          Logger.log(`current have Date: ${current.sku} - ${current.orderInjections} - ${current.date}`);
          // future có thông tin k cần push
          const arrFutureFind = arrFuture.find(
            (f) => f.sku === current.sku && f.orderInjections === current.orderInjections,
          );
          if (!arrFutureFind) {
            arrFuture.push(plainToInstance(PreProcessFutureDto, current));
          }
          return;
        }
        Logger.log(
          `================START SUGGEST DATE FOR PRICING ${current.sku} - ${current.orderInjections} ===================`,
        );
        const date = this.getDateTheBestForPricing(current, arrFuture, suggestScheduleDto?.expectedDate);
        current.date = date;
        Logger.log(`DATE FINAL: ${date}`);
        Logger.log(
          `================END SUGGEST DATE FOR PRICING ${current.sku} - ${current.orderInjections} ===================`,
        );
        arrFuture.push(plainToInstance(PreProcessFutureDto, current));
      });
    });

    // sort by date
    preProcessData.arrFuture = _.sortBy(preProcessData.arrFuture, 'date');
    preProcessData.arrCurrent = this.groupVaccineBySku(preProcessData.arrCurrent, suggestScheduleDto?.expectedDate);
    preProcessData.arrCurrent = this.scheduleRulesService.addScheduleAppointmentReminder(preProcessData.arrCurrent);

    return plainToInstance(SuggestScheduleRes, preProcessData, {
      excludeExtraneousValues: true,
      exposeUnsetFields: false,
    });
  }
}
