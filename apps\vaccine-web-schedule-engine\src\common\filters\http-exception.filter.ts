/* eslint-disable prefer-const */
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Inject,
  LoggerService,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { IError, IErrorResponse } from '../interfaces';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(@Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const status: HttpStatus = exception.getStatus();
    const exceptionResponse = exception.getResponse() as IError;

    const httpErrorResponse: IErrorResponse = {
      coreUrl: exceptionResponse.config?.baseURL + exceptionResponse.config?.url || '',
      status: status || HttpStatus.INTERNAL_SERVER_ERROR,
      error: { ...exceptionResponse, config: null },
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    };
    const timeExecuted = Date.now() - (request as any).startTime || 0;
    // elk logging errors
    this.logger.error(
      {
        message: 'HttpException',
        fields: {
          info: `${request.method}: ${request.originalUrl} - ${httpErrorResponse.status} - ${timeExecuted}ms`,
          method: request.method,
          url: request.originalUrl,
          bodyReq: JSON.stringify(request.body || {}),
          queryReq: JSON.stringify(request.query || {}),
          paramsReq: JSON.stringify(request.params || {}),
          headers: JSON.stringify(request.headers || {}),
          status: httpErrorResponse.status,
          timeExecuted,
          unitTime: 'ms',
          dataRes: JSON.stringify(httpErrorResponse),
        },
      },
      'HttpException',
      false,
    );

    response.status(httpErrorResponse.status).json(httpErrorResponse);
  }
}
