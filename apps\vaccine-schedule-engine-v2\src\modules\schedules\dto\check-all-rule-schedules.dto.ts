import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { CurrentDto } from './common';
import _ from 'lodash';
import { GUID_EMPTY } from 'apps/vaccine-schedule-engine-v2/src/constants';
import { DiseaseGroupIdInCart } from './extra.dto';
import { ScreenType } from '../constants';

export class CheckAllRuleSchedulesDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @Type(() => CurrentDto)
  @ValidateNested({ each: true })
  @Transform(({ value, obj }) => {
    _.forEach(value, (item) => {
      item['screenType'] = obj?.screenType || '';
    });
    value = value?.filter((current) => !(!current?.regimenId || current?.regimenId === GUID_EMPTY));
    return value;
  })
  arrCurrent: Array<CurrentDto>;

  @ApiHideProperty()
  @IsOptional()
  @Expose()
  arrFuture: [];

  @ApiHideProperty()
  @IsOptional()
  @Expose()
  expectedDate: Date;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value || 'INDICATION')
  screenType?: ScreenType;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  ticketCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  listDiseaseGroupIdInCart?: DiseaseGroupIdInCart[];

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  orderAttribute?: number;
}

export class CheckAllRuleSchedulesRes {
  @ApiProperty({ isArray: true, type: CurrentDto })
  @Expose()
  @IsOptional()
  items?: CurrentDto[];
}
