import { applyDecorators } from '@nestjs/common';
import { ApiHeaders, getSchemaPath } from '@nestjs/swagger';
import { ClassResponse } from './class-response';

export function generalSchema(Dto: any, type: 'object' | 'array' | 'string' | 'boolean') {
  let data: any = { type: type };
  switch (type) {
    case 'object':
      data = { ...data, $ref: getSchemaPath(Dto) };
      break;
    case 'array':
      data = { ...data, items: { $ref: getSchemaPath(Dto) } };
      break;
    default:
      break;
  }

  return {
    allOf: [
      { $ref: getSchemaPath(ClassResponse) },
      {
        properties: {
          data: data,
        },
      },
    ],
  };
}

export function CustomHeaders() {
  return applyDecorators(
    ApiHeaders([
      {
        name: 'shop-code',
        schema: {
          type: 'string',
          default: '58005',
        },
      },
      {
        name: 'order-channel',
        schema: {
          type: 'string',
          default: '14',
        },
      },
    ]),
  );
}
