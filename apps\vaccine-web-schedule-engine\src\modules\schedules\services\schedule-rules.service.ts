import { Injectable, Logger } from '@nestjs/common';
import _ from 'lodash';
import moment from 'moment';
import { ErrorCode } from 'apps/vaccine-schedule-engine-v2/src/common/errors/error-code';
import {
  DEFAULT_DATE_ADD,
  EnumDistanceType,
  RSA_ECOM_HARD_DEFAULT_SHOP_CODE,
  RuleType,
} from 'apps/vaccine-schedule-engine-v2/src/constants';
import { addDate, diffDate, isSameDate } from 'apps/vaccine-schedule-engine-v2/src/utilities/function-common';
import { GetEvaluationRequestByLcvIdRes } from 'vac-nest-examination';
import { AgeUnit, RegimenRulesDto } from 'vac-nest-regimen';
import { RuleRes } from '../dto/common';
import { PreProcessCurrentDto, PreProcessFutureDto } from '../dto/pre-process-data.dto';
import { AgeRuleService } from './age-rule.service';
import { RegimenRulesType, ScreenType, UnitFromName } from '../constants';

@Injectable()
export class ScheduleRulesService {
  constructor(protected readonly ageRuleService: AgeRuleService) {}
  /**
   * @description Lấy khoản cách giữa các lần tiêm theo thứ tự tiêm
   */
  getDistanceByOrderInjection(current: PreProcessCurrentDto) {
    const { orderInjections } = current;
    if (!current?.regimen?.details?.length)
      return { distanceType: 0, distanceValueByType: 0, minDistance: 0, dayAllowEarly: 0 };
    /**
     * @TODO check mũi nhắc lại trước khi xử lý
     */
    const detailRepeatFrequency = current?.regimen?.details?.find((e) => e.repeatFrequency);
    if (detailRepeatFrequency && orderInjections > current?.regimen?.details?.length) {
      const { distanceType, distanceValueByType, minDistance } = detailRepeatFrequency;
      return {
        distanceType,
        distanceValueByType,
        minDistance,
        dayAllowEarly: current?.regimen?.details?.at(0)?.dayAllowEarly,
        detailRepeatFrequency: detailRepeatFrequency,
      };
    }

    let injection = orderInjections % current?.regimen?.details?.length;
    if (injection === 0) injection = current?.regimen?.details?.length;
    // div
    const detailFind = current?.regimen?.details?.find((e) => e.order === injection);
    if (!detailFind)
      return {
        distanceType: 0,
        distanceValueByType: 0,
        minDistance: 0,
        dayAllowEarly: current?.regimen?.details?.at(0)?.dayAllowEarly,
      };
    const { distanceType, distanceValueByType, minDistance } = detailFind;
    if (injection === 1)
      return {
        distanceType: 0,
        distanceValueByType: 30,
        minDistance: 0,
        dayAllowEarly: current?.regimen?.details?.at(0)?.dayAllowEarly,
      };
    return {
      distanceType,
      distanceValueByType,
      minDistance,
      dayAllowEarly: current?.regimen?.details?.at(0)?.dayAllowEarly,
    };
  }

  /**
   * @description Cộng ngày/tháng/năm theo đỉnh nghĩa
   */
  addDateInDefined(date: Date, distanceType: number, distanceValueByType: number) {
    const newDate = moment(date);
    switch (distanceType) {
      case EnumDistanceType.days:
        newDate.add(distanceValueByType, 'days');
        break;
      case EnumDistanceType.months:
        newDate.add(distanceValueByType, 'months');
        break;
      case EnumDistanceType.years:
        newDate.add(distanceValueByType, 'years');
        break;
      default:
        break;
    }
    return new Date(newDate.format());
  }

  checkRuleAge(date: Date, current: PreProcessCurrentDto, rules: RuleRes[]) {
    Logger.log(
      `[USER AGE]: ${current.sku} - ${current.orderInjections} - birthDay ${current?.person?.dateOfBirth} - from ${
        current?.regimen?.ageFromValue
      } - to ${current?.regimen?.ageToValue} -  ageUnitCode ${
        current?.regimen?.ageFromUnitCode
      } - ${date.toISOString()}`,
    );
    // if (!current?.person?.dateOfBirth) return false;
    // rule chặn chưa đủ tuổi
    const isNotOldAge = this.ageRuleService.isNotOldAge(date, current, rules);
    // rule chặn quá tuổi
    const isOverAge = this.ageRuleService.isOverAge(date, current, rules);
    if (isNotOldAge) return isNotOldAge;
    if (isOverAge) return isOverAge;
    return false;
  }

  /**
   * @TODO Check rule tồn
   */
  checkRuleInventory(
    date: Date,
    current: PreProcessCurrentDto,
    rules: RuleRes[],
    shopCode: string,
    isCheck = false,
    screenType?: string,
  ) {
    if (current?.isStockInTicket) {
      // Handle in case sku cuối cùng đã được book cho chính ticket này
      return true;
    }
    const defaultDateAddWithOutStock = current?.isRareGood
      ? +process.env.DEFAULT_DATE_ADD_WITH_NOT_STOCK_RARE_GOOD
      : +process.env.DEFAULT_DATE_ADD_WITH_NOT_STOCK;

    if (
      (current?.stock?.quantityAvailable <= 0 || !current?.stock?.quantityAvailable) &&
      isSameDate(date, new Date()) &&
      shopCode !== RSA_ECOM_HARD_DEFAULT_SHOP_CODE
    ) {
      const dataRule = {
        message: `Rất tiếc, Sản phẩm ${current.sku} này đã hết tồn kho. Vui lòng nhập thêm hàng!`,
        ruleType: RuleType.HetTon,
        ruleName: 'Sản phẩm hết tồn',
      };

      if (current?.isRareGood) {
        const dateBuy = addDate(date, defaultDateAddWithOutStock, 'days');
        dataRule.message = `Sản phẩm có thuộc tính HÀNG KHAN HIẾM, số lượng tồn kho không đủ để đáp ứng nhu cầu mua mới. Vui lòng đặt lịch hẹn cho khách từ ngày ${dateBuy
          ?.utcOffset(7)
          ?.format('DD/MM/YYYY')}`;
        dataRule.ruleType = RuleType.HetTonHangKhangHiem;
        dataRule.ruleName = 'Sản phẩm khan hiếm hết tồn sau khi trừ lịch hẹn đã thanh toán/TTTP';
      }
      Logger.log(
        `[STOCK]: INPUT: ${date} - SHOP: ${shopCode}  ${current.sku} - ${current.orderInjections} - ${current.stock?.quantityAvailable} - Hàng khang hiếm:${current.stock?.['isRareGood']}`,
      );
      Logger.log(dataRule.message);
      if (
        !(isCheck && screenType === 'ECOM_PAYCHECK') ||
        (isCheck && screenType === 'ECOM_PAYCHECK' && current?.isRareGood)
      ) {
        rules.push({
          text: dataRule.message,
          type: current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
          value: defaultDateAddWithOutStock,
          ruleType: dataRule.ruleType,
          ruleName: dataRule.ruleName,
        });
      }
      return true;
    }

    // Chỉ check với hàng khang hiếm
    const countDate = diffDate(new Date(), date, 'days');
    if (
      (current?.stock?.quantityAvailable <= 0 || !current?.stock?.quantityAvailable) &&
      countDate > 0 &&
      countDate < defaultDateAddWithOutStock &&
      shopCode !== RSA_ECOM_HARD_DEFAULT_SHOP_CODE &&
      current?.isRareGood &&
      current?.status === 0
    ) {
      const dateBuy = addDate(date, defaultDateAddWithOutStock - countDate, 'days');
      const dataRule = {
        message: `Sản phẩm có thuộc tính HÀNG KHAN HIẾM, số lượng tồn kho không đủ để đáp ứng nhu cầu mua mới. Vui lòng đặt lịch hẹn cho khách từ ngày ${dateBuy
          ?.utcOffset(7)
          ?.format('DD/MM/YYYY')}`,
        ruleType: RuleType.HetTonHangKhangHiem,
        ruleName: 'Sản phẩm khan hiếm hết tồn sau khi trừ lịch hẹn đã thanh toán/TTTP',
      };
      Logger.log(
        `[STOCK]: INPUT: ${date} - SHOP: ${shopCode}  ${current.sku} - ${current.orderInjections} - ${current.stock?.quantityAvailable}`,
      );
      Logger.log(dataRule.message);
      if (
        !(isCheck && screenType === 'ECOM_PAYCHECK') ||
        (isCheck && screenType === 'ECOM_PAYCHECK' && current?.isRareGood)
      ) {
        rules.push({
          text: dataRule.message,
          type: current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
          value: defaultDateAddWithOutStock - countDate,
          ruleType: dataRule.ruleType,
          ruleName: dataRule.ruleName,
        });
      }
      return true;
    }
  }

  /**
   * @TODO Check rule 2 tiêm 1 uống/1 ngày
   */
  checkRuleConflict(date: Date, current: PreProcessCurrentDto, future: Array<PreProcessFutureDto>, rules: RuleRes[]) {
    const futureFilterWithDate = future?.filter((e) => isSameDate(e.date, date));
    const arrDiseaseGroupId = futureFilterWithDate?.map((e) => e?.diseaseGroupId);

    let injectionTimes = 0;
    let drinkTimes = 0;
    _.forEach(futureFilterWithDate, (f: PreProcessFutureDto) => {
      if (f?.regimen?.injectionRoutes?.at(0)?.usage !== 'Uống') {
        Logger.log(
          `[Injection]: ${current.sku} - ${current.orderInjections} - ${f.sku} - ${
            f?.regimen?.injectionRoutes?.at(0)?.usage
          }`,
        );
        ++injectionTimes;
      } else {
        Logger.log(
          `[DRINK]: ${current.sku} - ${current.orderInjections} - ${f.sku} - ${
            f?.regimen?.injectionRoutes?.at(0)?.usage
          }`,
        );
        ++drinkTimes;
      }
    });

    if (current?.regimen?.injectionRoutes?.at(0)?.usage !== 'Uống') {
      Logger.log(
        `[Current Injection]: ${current.sku} - ${current.orderInjections} - ${current.sku} - ${
          current?.regimen?.injectionRoutes?.at(0)?.usage
        }`,
      );
      ++injectionTimes;
    } else {
      Logger.log(
        `[Drink Injection]: ${current.sku} - ${current.orderInjections} - ${current.sku} - ${
          current?.regimen?.injectionRoutes?.at(0)?.usage
        }`,
      );
      ++drinkTimes;
    }
    Logger.log(
      `[TIMES]: ${current.sku} - ${current.orderInjections} - Injection: ${injectionTimes} - Drink: ${drinkTimes}`,
    );

    if (arrDiseaseGroupId?.includes(current?.diseaseGroupId)) {
      const regimenOld = futureFilterWithDate?.find(
        (futureItem) => futureItem?.diseaseGroupId === current?.diseaseGroupId,
      );

      rules.push({
        text: `Khách hàng đã có lịch tiêm ngày ${moment(regimenOld?.date)
          .utcOffset(7)
          .format('DD/MM/YYYY')} cho vaccine ${regimenOld?.regimen?.vaccine?.name || 'có'} cùng nhóm bệnh ${
          regimenOld?.regimen?.diseaseGroup?.name || current?.regimen?.diseaseGroup?.name
        }. Vui lòng kiểm tra và đặt hẹn ngày khác.`,
        type: current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
        value: DEFAULT_DATE_ADD,
        ruleType: RuleType.HaiMuiCungNhomBenh,
        ruleName: 'Tiêm 2 mũi cùng nhóm bệnh cùng ngày',
        isAllowEvaluate: current?.screenType === 'INDICATION' ? true : false,
      });
      return true;
    }

    if (injectionTimes > 2 || drinkTimes >= 2) {
      rules.push({
        text: `Đã chỉ định đủ 2 mũi tiêm và 1 uống trong ngày ${moment(current.date)
          .utcOffset(7)
          .format('DD/MM/YYYY')}. Không được chỉ định thêm`,
        type: current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
        value: DEFAULT_DATE_ADD,
        ruleType: RuleType.HaiTiemMotUong,
        ruleName: 'Chỉ định quá 2 tiêm 1 uống trong cùng 1 ngày',
        isAllowEvaluate: current?.screenType === 'INDICATION' ? true : false,
      });
      return true;
    }
    return false;
  }

  /**
   * @TODO Check rule khoản kách giữa các lần tiêm
   * @TODO check rule đến sớm
   */
  checkRuleDistance(
    date: Date,
    current: PreProcessCurrentDto,
    arrFuture: Array<PreProcessFutureDto>,
    rules: RuleRes[],
  ) {
    // danh sách lịch tiêm quá khứ
    const arrPastFilter = arrFuture?.filter((e) => moment(date).utcOffset(7).isAfter(moment(e.date).utcOffset(7), 'D'));
    // danh sách lịch tiêm tương lai
    const arrFutureFilter = arrFuture?.filter((e) =>
      moment(date).utcOffset(7).isSameOrBefore(moment(e.date).utcOffset(7), 'D'),
    );

    // Filter vaccine interactions loại trừ interactionType === 5
    const filteredVaccineInteractions = current?.regimen?.vaccine?.vaccineInteractions?.filter(
      (interaction) => interaction.interactionType !== 5,
    );

    // loop lịch tiêm quá khứ
    for (const past of arrPastFilter) {
      const { minDistance, distanceType, distanceValueByType, detailRepeatFrequency } =
        this.getDistanceByOrderInjection(current);
      const diffDistance = diffDate(past.date, date, 'days');

      if (past.diseaseGroupId === current.diseaseGroupId && current?.orderInjections - 1 === past?.orderInjections) {
        const standardDate = this.addDateInDefined(new Date(past.date), distanceType, distanceValueByType);
        // tính ngày khoảng cách hiện tại với ngày hẹn
        const earlyDate = diffDate(date, standardDate, 'days');
        Logger.log(`Khoảng cách ngày hiện tại với ngày hẹn là: ${earlyDate} ngày`);
        // Nếu mũi nhắc thì lấy mũi mũi cúi cùng
        let orderCurrent = current?.orderInjections;
        if (detailRepeatFrequency) {
          const injectionInRegimen = current?.regimen?.details?.length;
          orderCurrent = injectionInRegimen;
        }

        const ruleRegimentByOrderInjection = current?.regimen?.details?.find((item) => item?.order === orderCurrent);

        const filterRuleDenSom =
          ruleRegimentByOrderInjection?.regimenRules?.length > 0
            ? ruleRegimentByOrderInjection?.regimenRules?.filter(
                (i) => i?.regimenRulesType === RegimenRulesType.DEN_SOM,
              )
            : [];
        Logger.log(`Filter rule đến sớm với regimenRulesType = 1: ${JSON.stringify(filterRuleDenSom)}`);

        // rule mới ngày 12/08/2024
        // case 1: đúng ngày tiêm
        if (filterRuleDenSom?.length > 0) {
          _.forEach(filterRuleDenSom, (item: RegimenRulesDto) => {
            if (earlyDate <= 0) return;
            else if (item?.from && item?.to) {
              // [], [), (], ()
              if (
                (item?.equalFrom && item?.equalTo && earlyDate >= item?.from && earlyDate <= item?.to) || // trong khoảng có điều kiện bằng trên và bằng dưới
                (item?.equalFrom && !item?.equalTo && earlyDate >= item?.from && earlyDate < item?.to) || // trong khoảng có điều kiện bằng dưới
                (!item?.equalFrom && item?.equalTo && earlyDate > item?.from && earlyDate <= item?.to) || // trong khoảng có điều kiện bằng trên
                (!item?.equalFrom && !item?.equalTo && earlyDate > item?.from && earlyDate < item?.to) // trong khoảng không có điều kiện bằng trên và dưới
              ) {
                Logger.log(
                  `Vào rule đến sớm trong khoảng ${item?.from} -> ${item?.to} với mũi thứ ${current?.orderInjections} của vaccine ${current?.regimen?.vaccine?.name} có alertLevel là ${item?.alertLevel}`,
                );
                const message =
                  item?.alertLevel === 1
                    ? ErrorCode.getError(ErrorCode.REGIMENT_RULE_TYPE_DISTANCE_WARING)
                        ?.replace('{order}', current?.orderInjections?.toString())
                        ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                        ?.replace('{earlyDate}', earlyDate?.toString())
                    : ErrorCode.getError(ErrorCode.REGIMENT_RULE_TYPE_DISTANCE_DANGER)
                        ?.replace('{order}', current?.orderInjections?.toString())
                        ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                        ?.replace('{earlyDate}', earlyDate?.toString())
                        ?.replace('{from}', item?.from?.toString())
                        ?.replace('{unitFrom}', UnitFromName[item?.fromUnit]);
                Logger.log(message);
                rules.push({
                  text: message,
                  type: item?.alertLevel === 1 || current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
                  value: earlyDate,
                  ruleType: RuleType.DenSom,
                  ruleName: 'Khách hàng đến sớm so với khoảng cách tiêu chuẩn của phác đồ',
                  isAllowEvaluate: item?.alertLevel !== 1 ? true : false,
                });
              }
            }
            if (item?.from && !item?.to) {
              // ngoài khoảng có điều kiện bằng dưới
              // ngoài khoảng không có điều kiện bằng dưới
              if ((item?.equalFrom && earlyDate >= item?.from) || (!item?.equalFrom && earlyDate > item?.from)) {
                Logger.log(
                  `Vào rule đến sớm từ ${item?.from} -> vô cùng với mũi thứ ${current?.orderInjections} của vaccine ${current?.regimen?.vaccine?.name} có alertLevel là ${item?.alertLevel}`,
                );
                const message =
                  item?.alertLevel === 1
                    ? ErrorCode.getError(ErrorCode.REGIMENT_RULE_TYPE_DISTANCE_WARING)
                        ?.replace('{order}', current?.orderInjections?.toString())
                        ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                        ?.replace('{earlyDate}', earlyDate?.toString())
                    : ErrorCode.getError(ErrorCode.REGIMENT_RULE_TYPE_DISTANCE_DANGER)
                        ?.replace('{order}', current?.orderInjections?.toString())
                        ?.replace('{vaccineName}', current?.regimen?.vaccine?.name)
                        ?.replace('{earlyDate}', earlyDate?.toString())
                        ?.replace('{from}', item?.from?.toString())
                        ?.replace('{unitFrom}', UnitFromName[item?.fromUnit]);
                Logger.log(message);
                rules.push({
                  text: message,
                  type: item?.alertLevel === 1 || current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
                  value: earlyDate,
                  ruleType: RuleType.DenSom,
                  ruleName: 'Khách hàng đến sớm so với khoảng cách tiêu chuẩn của phác đồ',
                  isAllowEvaluate: item?.alertLevel !== 1 ? true : false,
                });
              }
            }
          });
        }
      }

      // Khoảng cách tương tác (loại trừ interactionType === 5)
      const interactionFind = filteredVaccineInteractions?.find((e) => e.sku === past.sku);
      // case 1 cho phép tiêm cùng ngày đối với vaccine tương tác nếu isInjectionSameDay === true
      // check cùng ngày thì by pass
      // khác ngày thì báo lỗi
      // khoảng cách lớn hơn 0 và bé hơn minDistanceValue (khoảng cách regimen định nghĩa thì chặn)
      if (
        interactionFind &&
        interactionFind?.isInjectionSameDay &&
        diffDistance > 0 &&
        diffDistance < interactionFind?.minDistanceValue
      ) {
        Logger.log(`[INTERACTION PAST]: ${past.sku}  - ${current.sku} - ${interactionFind?.isInjectionSameDay}`);

        rules.push({
          text: interactionFind?.warningContent || '',
          type: this.ruleTypeDistance(current?.screenType, interactionFind?.warningType),
          value: interactionFind?.minDistanceValue,
          ruleType: RuleType.TuongTac,
          ruleName: `Tương tác vắc xin ${current?.regimen?.vaccine?.name} với ${
            past?.regimen?.vaccine?.name || past?.vaccineName
          }`,
          isAllowEvaluate: true,
        });
        return true;
      }
      // case 2 không cho phép tiêm cùng ngày đối với vaccine tương tác nếu isInjectionSameDay === false
      // nằm trong khoảng 1->27 ngày thì báo lỗi
      if (interactionFind && !interactionFind?.isInjectionSameDay && diffDistance < interactionFind.minDistanceValue) {
        Logger.log(
          `[INTERACTION PAST]: ${current.sku}  - ${current.orderInjections} - ${minDistance} - ${diffDistance}`,
        );
        rules.push({
          text: interactionFind?.warningContent || '',
          type: this.ruleTypeDistance(current?.screenType, interactionFind?.warningType),
          value: interactionFind?.minDistanceValue,
          ruleType: RuleType.TuongTac,
          ruleName: `Tương tác vắc xin ${current?.regimen?.vaccine?.name} với ${
            past?.regimen?.vaccine?.name || past?.vaccineName
          }`,
          isAllowEvaluate: true,
        });
        return true;
      }
    }

    // loop lịch tiêm tương lai
    for (const future of arrFutureFilter) {
      const { minDistance } = this.getDistanceByOrderInjection(current);
      const diffDistance = diffDate(date, future.date, 'days');

      if (future?.orderInjections < current?.orderInjections && current?.diseaseGroupId === future?.diseaseGroupId) {
        Logger.log(`[FUTURE]: ${current.sku} - ${current.orderInjections} - ${future?.orderInjections}`);
        Logger.log(
          `Khách hàng đã có lịch tiêm ngày ${moment(future.date).utcOffset(7).format('DD/MM/YYYY')} cho vaccine ${
            future?.regimen?.vaccine?.name || 'có'
          } cùng nhóm bệnh ${current?.regimen?.diseaseGroup?.name}. Vui lòng kiểm tra và đặt hẹn sau lịch tiêm trên.`,
        );
        rules.push({
          text: `Khách hàng đã có lịch tiêm ngày ${moment(future.date).utcOffset(7).format('DD/MM/YYYY')} cho vaccine ${
            future?.regimen?.vaccine?.name || 'có'
          } cùng nhóm bệnh ${current?.regimen?.diseaseGroup?.name}. Vui lòng kiểm tra và đặt hẹn sau lịch tiêm trên.`,
          type: current?.screenType === 'SLIDEBAR' ? 'WARNING' : 'DANGER',
          value: 0,
          ruleType: RuleType.HaiMuiCungNhomBenh,
          ruleName: 'Tiêm 2 mũi cùng nhóm bệnh cùng ngày',
        });
      }

      // Khoảng cách tương tác (loại trừ interactionType === 5)
      const interactionFind = filteredVaccineInteractions?.find((e) => e.sku === future.sku);
      // case 1 cho phép tiêm cùng ngày đối với vaccine tương tác nếu isInjectionSameDay === true
      // check cùng ngày thì by pass
      // khác ngày thì báo lỗi
      // khoảng cách lớn hơn 0 và bé hơn minDistanceValue (khoảng cách regimen định nghĩa thì chặn)
      if (
        interactionFind &&
        interactionFind?.isInjectionSameDay &&
        diffDistance > 0 &&
        diffDistance < interactionFind?.minDistanceValue
      ) {
        Logger.log(`[INTERACTION PAST]: ${future.sku}  - ${current.sku} - ${interactionFind?.isInjectionSameDay}`);
        rules.push({
          text: interactionFind?.warningContent || '',
          type: this.ruleTypeDistance(current?.screenType, interactionFind?.warningType),
          value: interactionFind?.minDistanceValue,
          ruleType: RuleType.TuongTac,
          ruleName: `Tương tác vắc xin ${current?.regimen?.vaccine?.name} với ${
            future?.regimen?.vaccine?.name || future?.vaccineName
          }`,
          isAllowEvaluate: true,
        });
        return true;
      }
      // case 2 không cho phép tiêm cùng ngày đối với vaccine tương tác nếu isInjectionSameDay === false
      // nằm trong khoảng 1->27 ngày thì báo lỗi
      if (interactionFind && !interactionFind?.isInjectionSameDay && diffDistance < interactionFind.minDistanceValue) {
        Logger.log(
          `[INTERACTION FUTURE]: ${current.sku} - ${current.orderInjections} - ${minDistance} - ${diffDistance}`,
        );
        rules.push({
          text: interactionFind?.warningContent || '',
          type: this.ruleTypeDistance(current?.screenType, interactionFind?.warningType),
          value: interactionFind?.minDistanceValue,
          ruleType: RuleType.TuongTac,
          ruleName: `Tương tác vắc xin ${current?.regimen?.vaccine?.name} với ${
            future?.regimen?.vaccine?.name || future?.vaccineName
          }`,
          isAllowEvaluate: true,
        });
        return true;
      }
    }

    // return false;
  }

  getEnumDistanceType(distanceType: number): moment.unitOfTime.Diff {
    switch (distanceType) {
      case EnumDistanceType.days:
        return 'days';
      case EnumDistanceType.months:
        return 'months';
      case EnumDistanceType.years:
        return 'years';
      default:
        break;
    }
  }

  /**
   * @description check rule đã mua/tiêm đủ phác đồ
   */
  checkRuleCompletedInjection(arrCurrentDto: Array<PreProcessCurrentDto>, regimenLength: number): boolean {
    // 0 : Hẹn, 1: đã thanh toán, 2; đã tiêm
    // danh sách đã tiêm
    const arrCurrentInjected = arrCurrentDto.filter((e) => e.status === 2);
    const arrCurrentBuyOrIsPaid = arrCurrentDto.filter((e) => e.status === 1 || (e.status === 0 && e.vaccinatedNow));
    Logger.log(`arrCurrentInjected length: ${JSON.stringify(arrCurrentInjected?.length)}`);
    Logger.log(`arrCurrentBuyOrIsPaid length: ${JSON.stringify(arrCurrentBuyOrIsPaid?.length)}`);
    // case 1: 2 mũi đã tiêm
    if (arrCurrentInjected.length >= regimenLength) return true;
    // tổng hợp mũi đã mua trong đơn hoặc dẵ thanh toán và đã tiêm >= phắc đồ chuẩn thì có nghĩa đã pass rule
    else if (arrCurrentInjected.length + arrCurrentBuyOrIsPaid.length >= regimenLength) return true;
    return false;
  }

  /**
   * thêm mũi nhắc hẹn
   */
  addScheduleAppointmentReminder(arrCurrent: Array<PreProcessCurrentDto>) {
    const arrCurrentNew: Array<PreProcessCurrentDto> = arrCurrent;
    // group lịch hẹn theo DiseaseGroupId
    const groupedData = _.groupBy(
      arrCurrent,
      (entry: PreProcessCurrentDto) => `${entry.diseaseGroupId}-${entry.lcvId}`,
    );

    _.forEach(groupedData, (arrCurrentDto: Array<PreProcessCurrentDto>) => {
      //get last Item
      // We need to get the most recent regimen by date rather than by index
      // because appointments may be created out of order or rescheduled
      // and the most recent appointment is more likely to have the correct information

      // Ensure we have valid dates before sorting
      const validDateEntries = arrCurrentDto.filter((item) => item.date && !isNaN(new Date(item.date).getTime()));

      // Use lodash orderBy for more consistent sorting across browsers/environments
      // Sort by date in descending order (newest first) to get the most recent regimen
      const sortedByDate = _.orderBy(validDateEntries, [(item) => new Date(item.date).getTime()], ['desc']);

      // Get the most recent regimen and item
      const regimenLast = sortedByDate[0]?.regimen;
      const itemLast = sortedByDate[0];

      // TODO: Add unit tests to verify this logic works correctly with:
      // - Empty arrays
      // - Invalid dates
      // - Multiple entries with the same date
      // - Entries with missing regimen data
      // check phac do co mui nhac lai hay k?
      const detailRepeatFrequency = regimenLast?.details?.find((e) => e.repeatFrequency);
      Logger.log(`detailRepeatFrequency: ${JSON.stringify(detailRepeatFrequency)}`);
      if (!detailRepeatFrequency) return;
      // check 6 case than thanh
      const isCompletedInjection = this.checkRuleCompletedInjection(arrCurrentDto, regimenLast?.details?.length || 0);
      Logger.log(`isCompletedInjection: ${isCompletedInjection}`);
      if (!isCompletedInjection) return;

      const arrCurrentSchedule = arrCurrentDto.filter((e) => e.status === 0 && !e.vaccinatedNow);
      Logger.log(`arrCurrentSchedule: ${JSON.stringify(arrCurrentSchedule)}`);
      if (arrCurrentSchedule?.length) return;

      // đi push zo data
      const { distanceType, distanceValueByType } = detailRepeatFrequency;
      arrCurrentNew.push({
        ...itemLast,
        status: 0, // tiêm sau
        vaccinatedNow: null,
        orderInjections: itemLast.orderInjections + 1,
        date: this.addDateInDefined(new Date(itemLast?.date), distanceType, distanceValueByType).toISOString(),
      });

      Logger.log(
        `arrCurrentNew.push: ${JSON.stringify({
          ...itemLast,
          status: 0, // tiêm sau
          vaccinatedNow: null,
          orderInjections: itemLast.orderInjections + 1,
          date: this.addDateInDefined(new Date(itemLast?.date), distanceType, distanceValueByType).toISOString(),
        })}`,
      );
      Logger.log(`===================DONE ${itemLast.diseaseGroupId}============================`);
    });

    return arrCurrentNew;
  }

  /**
   * @TODO check rule chặn chưa đủ tuổi
   */
  isNotOld(
    ageFromUnitCode: number,
    ageFromValue: number,
    equalFromAge: boolean,
    { months: month, weeks: week, years: age },
  ): boolean {
    Logger.log(`[isNotOld]: ${ageFromUnitCode} - ${ageFromValue} - ${equalFromAge}`);
    Logger.log(`[WEEK]: ${ageFromUnitCode} - ${week} - ${ageFromValue}`);
    if (ageFromUnitCode === AgeUnit.Week && (week < ageFromValue || (!equalFromAge && week === ageFromValue)))
      return true;
    Logger.log(`[MONTH]: ${ageFromUnitCode} - ${month} - ${ageFromValue} - ${equalFromAge}`);
    if (ageFromUnitCode === AgeUnit.Month && (month < ageFromValue || (!equalFromAge && month === ageFromValue)))
      return true;
    Logger.log(`[AGE]: ${ageFromUnitCode} - ${age} - ${ageFromValue} - ${equalFromAge}`);
    if (ageFromUnitCode === AgeUnit.Age && (age < ageFromValue || (!equalFromAge && age === ageFromValue))) return true;

    return false;
  }

  /**
   * @TODO check rule chặn quá tuổi
   */
  isOverAge(
    ageFromUnitCode: number,
    ageToValue: number,
    { remainingDaysInYears, remainingDaysInMonths, remainingDaysInWeeks, months: month, weeks: week, years: age },
    current: PreProcessCurrentDto,
    equalToAge: boolean,
  ): boolean {
    Logger.log(`[isOverAge]: ${ageFromUnitCode} - ${ageToValue} - ${equalToAge}`);
    if (ageFromUnitCode === AgeUnit.Week) {
      Logger.log(
        `[WEEK]: ${`ageFromUnitCode:` + ageFromUnitCode} - ${`week:` + week} - ${`ageToValue:` + ageToValue} - ${
          `orderInjections:` + current?.orderInjections
        } - ${`remainingDaysInWeeks:` + remainingDaysInWeeks} - ${`equalToAge:` + equalToAge}`,
      );
      if (week > ageToValue || (equalToAge && week === ageToValue && remainingDaysInWeeks !== 0)) return true;
    } else if (ageFromUnitCode === AgeUnit.Month) {
      Logger.log(
        `[MONTH]: ${`ageFromUnitCode:` + ageFromUnitCode} - ${`month:` + month} - ${`ageToValue:` + ageToValue} - ${
          `orderInjections:` + current?.orderInjections
        } - ${`remainingDaysInMonths:` + remainingDaysInMonths} - ${`equalToAge:` + equalToAge}`,
      );
      if (month > ageToValue || (equalToAge && month === ageToValue && remainingDaysInMonths !== 0)) return true;
    } else if (ageFromUnitCode === AgeUnit.Age) {
      Logger.log(
        `[AGE]: ${`ageFromUnitCode:` + ageFromUnitCode} - ${`age:` + age} - ${`ageToValue:` + ageToValue} - ${
          `orderInjections:` + current?.orderInjections
        } - ${`remainingDaysInYears:` + remainingDaysInYears} - ${`equalToAge:` + equalToAge}`,
      );
      if (age > ageToValue || (equalToAge && age === ageToValue && remainingDaysInYears !== 0)) return true;
    }

    return false;
  }

  /**
   * @description add rule cần review
   */
  addRuleNeedReview(arrCurrent: PreProcessCurrentDto[], evaluationRequestSortDate: GetEvaluationRequestByLcvIdRes[]) {
    // check phíu trước đó đã được duyệt
    const evaluationRequestSortDateFind = evaluationRequestSortDate
      .filter((e) => e.status === 1 && isSameDate(new Date(), new Date(e.createdDate)))
      ?.at(0);
    if (!evaluationRequestSortDateFind) {
      arrCurrent.forEach((e) => {
        e?.rules?.forEach((ruleEntry) => {
          ruleEntry['needReview'] = false;
          if (ruleEntry?.type === 'DANGER') {
            ruleEntry['needReview'] = true;
          }
        });
      });
      return arrCurrent;
    }
    const arrCurrentNew: PreProcessCurrentDto[] = JSON.parse(JSON.stringify(arrCurrent));
    const getDetailDaDuyet = evaluationRequestSortDateFind?.details;
    arrCurrentNew.forEach((e) => {
      e?.rules?.forEach((ruleEntry) => {
        const getDetailDaDuyetFind = getDetailDaDuyet.find(
          (f) =>
            f.regimenId === e.regimenId &&
            f.orderInjections === e.orderInjections &&
            f.ruleType === ruleEntry.ruleType &&
            isSameDate(new Date(f.appointmentDate), new Date(e.date)),
        );
        ruleEntry['needReview'] = false;
        if (ruleEntry?.type === 'DANGER') {
          ruleEntry['needReview'] = true;
        }
        // đã duyệt thì k cần review
        if (getDetailDaDuyetFind) {
          ruleEntry['needReview'] = false;
        }
      });
    });
    return arrCurrentNew;
  }

  checkSuggestCRHoliday(date?: Date) {
    const dateStart = process.env.FROM_DATE; // ngày hoành đạo cuối năm
    const dateEnd = process.env.TO_DATE; // ngày hoành đạo cuối năm
    const dateSuggestHoliday = '2025-02-03'; // ngày may mắn đầu năm
    const isBetweenTet = moment(moment(date).utcOffset(7).format('YYYY-MM-DD')).isBetween(
      dateStart,
      dateEnd,
      undefined,
      '[]',
    );
    if (isBetweenTet) {
      date = new Date(moment(new Date(dateSuggestHoliday)).utcOffset(7).format('YYYY-MM-DD'));
    }
    return date;
  }
  /**
   * @description hiển thị type warning || danger cho rule type tương tác vaccine
   *
   * Note: hiển thị type
   * cũ: interactionType = 4 nhưng nhu cầu k cần thiết nên không dùng để check type nữa
   * mới: dựa vào các điều kiện sau
   *  1: Nếu screenType === SLIDEBAR => warning
   *  2: Nếu screenType !== SLIDEBAR thì check thêm điều kiện dưới
   *      + warningType = 0 => danger
   *      + warningType = 1 => warning
   */
  private ruleTypeDistance(screenType?: ScreenType, warningType?: number) {
    if (screenType === 'SLIDEBAR' || warningType === 1) return 'WARNING';
    // if (warningType === 0) return 'DANGER';
    return 'DANGER';
  }

  /**
   * @TODO check rule cho web thao tác sau 4h chiều ngày D thì suggest lại D+1
   */
  ruleAfterFourAfternoonForWeb(date?: Date) {
    const dateNow = new Date();
    if (
      isSameDate(dateNow, date) &&
      moment(date).utcOffset(7).hour() >= parseInt(process.env.BUSINESS_DAY_END_HOUR || '16')
    ) {
      date = this.addDateInDefined(date, 0, 1); // +1 ngày
    }
    Logger.log(`Rule 4h chiều => dateNow: ${JSON.stringify(dateNow)}`);
    Logger.log(`Rule 4h chiều => date: ${JSON.stringify(date)}`);
    Logger.log(`Rule 4h chiều => getHours: ${JSON.stringify(moment(date).utcOffset(7).hour())}`);
    Logger.log(`Rule 4h chiều => isSameDate: ${isSameDate(dateNow, date)}`);
    return date;
  }
}
